package com.example.tp2_panier_d_achat.ui.adapters

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.example.tp2_panier_d_achat.ui.magasin.MagasinFragment
import com.example.tp2_panier_d_achat.ui.panier.PanierFragment

/**
 * Adapter pour ViewPager2 qui gère la navigation entre les fragments
 */
class ViewPagerAdapter(fragmentActivity: FragmentActivity) : FragmentStateAdapter(fragmentActivity) {

    override fun getItemCount(): Int = 2

    override fun createFragment(position: Int): Fragment {
        return when (position) {
            0 -> MagasinFragment()
            1 -> PanierFragment()
            else -> throw IllegalArgumentException("Position invalide: $position")
        }
    }
}
