<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="48dp"
    android:height="48dp"
    android:viewportWidth="48"
    android:viewportHeight="48">
    
    <!-- Background circle -->
    <path
        android:fillColor="#424242"
        android:pathData="M24,44 Q34,44 39,39 Q44,34 44,24 Q44,14 39,9 Q34,4 24,4 Q14,4 9,9 Q4,14 4,24 Q4,34 9,39 Q14,44 24,44 Z" />
    
    <!-- Basket base -->
    <path
        android:fillColor="@android:color/transparent"
        android:strokeColor="#FFFFFF"
        android:strokeWidth="2"
        android:pathData="M12,28 L36,28 L34,38 L14,38 Z" />
    
    <!-- Basket handle left -->
    <path
        android:fillColor="@android:color/transparent"
        android:strokeColor="#FFFFFF"
        android:strokeWidth="2"
        android:strokeLineCap="round"
        android:pathData="M12,28 Q10,24 12,20" />
    
    <!-- Basket handle right -->
    <path
        android:fillColor="@android:color/transparent"
        android:strokeColor="#FFFFFF"
        android:strokeWidth="2"
        android:strokeLineCap="round"
        android:pathData="M36,28 Q38,24 36,20" />
    
    <!-- Items in basket - Bread/Baguette -->
    <path
        android:fillColor="@android:color/transparent"
        android:strokeColor="#FFFFFF"
        android:strokeWidth="1.5"
        android:strokeLineCap="round"
        android:pathData="M16,26 L32,22" />
    
    <!-- Items in basket - Bottle -->
    <path
        android:fillColor="@android:color/transparent"
        android:strokeColor="#FFFFFF"
        android:strokeWidth="1.5"
        android:strokeLineCap="round"
        android:pathData="M20,26 L20,18 M18,18 L22,18" />
    
    <!-- Items in basket - Can/Package -->
    <path
        android:fillColor="@android:color/transparent"
        android:strokeColor="#FFFFFF"
        android:strokeWidth="1.5"
        android:pathData="M26,26 L26,20 L30,20 L30,26" />
    
    <!-- Items in basket - Small package -->
    <path
        android:fillColor="@android:color/transparent"
        android:strokeColor="#FFFFFF"
        android:strokeWidth="1.5"
        android:pathData="M28,18 L32,18 L32,22 L28,22 Z" />
    
    <!-- Basket bottom lines for depth -->
    <path
        android:fillColor="@android:color/transparent"
        android:strokeColor="#FFFFFF"
        android:strokeWidth="1"
        android:strokeAlpha="0.6"
        android:pathData="M14,32 L34,32" />
    <path
        android:fillColor="@android:color/transparent"
        android:strokeColor="#FFFFFF"
        android:strokeWidth="1"
        android:strokeAlpha="0.4"
        android:pathData="M15,35 L33,35" />
    
</vector>
