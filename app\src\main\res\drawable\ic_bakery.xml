<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="48dp"
    android:height="48dp"
    android:viewportWidth="48"
    android:viewportHeight="48">
    
    <!-- Baguette -->
    <path
        android:fillColor="#D7CCC8"
        android:strokeColor="#8D6E63"
        android:strokeWidth="1"
        android:pathData="M4,20 L28,16 Q30,16 30,18 Q30,20 28,20 L4,24 Q2,24 2,22 Q2,20 4,20 Z" />
    
    <!-- Baguette cuts -->
    <path
        android:fillColor="@android:color/transparent"
        android:strokeColor="#8D6E63"
        android:strokeWidth="0.8"
        android:pathData="M8,19 L8,21" />
    <path
        android:fillColor="@android:color/transparent"
        android:strokeColor="#8D6E63"
        android:strokeWidth="0.8"
        android:pathData="M12,18.5 L12,20.5" />
    <path
        android:fillColor="@android:color/transparent"
        android:strokeColor="#8D6E63"
        android:strokeWidth="0.8"
        android:pathData="M16,18 L16,20" />
    <path
        android:fillColor="@android:color/transparent"
        android:strokeColor="#8D6E63"
        android:strokeWidth="0.8"
        android:pathData="M20,17.5 L20,19.5" />
    <path
        android:fillColor="@android:color/transparent"
        android:strokeColor="#8D6E63"
        android:strokeWidth="0.8"
        android:pathData="M24,17 L24,19" />
    
    <!-- Croissant -->
    <path
        android:fillColor="#FFD54F"
        android:strokeColor="#FF8F00"
        android:strokeWidth="0.8"
        android:pathData="M32,28 Q34,26 36,28 Q38,30 36,32 Q34,34 32,32 Q30,30 32,28 Z" />
    
    <!-- Croissant layers -->
    <path
        android:fillColor="@android:color/transparent"
        android:strokeColor="#FF8F00"
        android:strokeWidth="0.5"
        android:pathData="M33,29 Q34,28 35,29" />
    <path
        android:fillColor="@android:color/transparent"
        android:strokeColor="#FF8F00"
        android:strokeWidth="0.5"
        android:pathData="M33,31 Q34,30 35,31" />
    
    <!-- Round bread -->
    <path
        android:fillColor="#D7CCC8"
        android:strokeColor="#8D6E63"
        android:strokeWidth="1"
        android:pathData="M8,36 Q8,32 12,32 Q16,32 16,36 Q16,40 12,40 Q8,40 8,36 Z" />
    
    <!-- Round bread cross -->
    <path
        android:fillColor="@android:color/transparent"
        android:strokeColor="#8D6E63"
        android:strokeWidth="1"
        android:pathData="M10,36 L14,36" />
    <path
        android:fillColor="@android:color/transparent"
        android:strokeColor="#8D6E63"
        android:strokeWidth="1"
        android:pathData="M12,34 L12,38" />
    
    <!-- Pretzel -->
    <path
        android:fillColor="#8D6E63"
        android:strokeColor="#5D4037"
        android:strokeWidth="0.8"
        android:pathData="M20,36 Q18,34 20,32 Q22,34 24,32 Q26,34 24,36 Q22,38 20,36 Z" />
    
    <!-- Pretzel holes -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M20,34 Q20,33 21,33 Q22,33 22,34 Q22,35 21,35 Q20,35 20,34 Z" />
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M22,36 Q22,35 23,35 Q24,35 24,36 Q24,37 23,37 Q22,37 22,36 Z" />
    
    <!-- Pretzel salt -->
    <path
        android:fillColor="#FFFFFF"
        android:fillAlpha="0.8"
        android:pathData="M19,35 L19.5,35 L19.5,35.5 L19,35.5 Z" />
    <path
        android:fillColor="#FFFFFF"
        android:fillAlpha="0.8"
        android:pathData="M21,37 L21.5,37 L21.5,37.5 L21,37.5 Z" />
    <path
        android:fillColor="#FFFFFF"
        android:fillAlpha="0.8"
        android:pathData="M23,33 L23.5,33 L23.5,33.5 L23,33.5 Z" />
    
    <!-- Donut -->
    <path
        android:fillColor="#8D6E63"
        android:pathData="M30,40 Q30,36 34,36 Q38,36 38,40 Q38,44 34,44 Q30,44 30,40 Z" />
    
    <!-- Donut hole -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M32,40 Q32,38 34,38 Q36,38 36,40 Q36,42 34,42 Q32,42 32,40 Z" />
    
    <!-- Donut glaze -->
    <path
        android:fillColor="#E91E63"
        android:fillAlpha="0.7"
        android:pathData="M30.5,39 Q32,37 35,38 Q37,39 37.5,41 Q36,42 34,42 Q32,41 30.5,39 Z" />
    
    <!-- Muffin -->
    <path
        android:fillColor="#8D6E63"
        android:strokeColor="#5D4037"
        android:strokeWidth="0.8"
        android:pathData="M40,40 L44,40 L44,36 Q44,34 42,34 Q40,34 40,36 Z" />
    
    <!-- Muffin top -->
    <path
        android:fillColor="#D7CCC8"
        android:strokeColor="#8D6E63"
        android:strokeWidth="0.8"
        android:pathData="M39,36 L45,36 Q45,32 42,32 Q39,32 39,36 Z" />
    
    <!-- Muffin blueberries -->
    <path
        android:fillColor="#3F51B5"
        android:pathData="M41,34 Q41,33 42,33 Q43,33 43,34 Q43,35 42,35 Q41,35 41,34 Z" />
    <path
        android:fillColor="#3F51B5"
        android:pathData="M40,35 Q40,34 41,34 Q42,34 42,35 Q42,36 41,36 Q40,36 40,35 Z" />
    
    <!-- Wheat decoration -->
    <path
        android:fillColor="#FF8F00"
        android:strokeColor="#F57C00"
        android:strokeWidth="0.5"
        android:pathData="M6,8 L6,14" />
    <path
        android:fillColor="#FF8F00"
        android:pathData="M5,8 L7,8 L7,9 L5,9 Z" />
    <path
        android:fillColor="#FF8F00"
        android:pathData="M5,10 L7,10 L7,11 L5,11 Z" />
    <path
        android:fillColor="#FF8F00"
        android:pathData="M5,12 L7,12 L7,13 L5,13 Z" />
    
</vector>
