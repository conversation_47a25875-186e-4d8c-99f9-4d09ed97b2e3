<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_magasin" modulePackage="com.example.tp2_panier_d_achat" filePath="app\src\main\res\layout\item_magasin.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView" rootNodeViewId="@+id/card_view_item"><Targets><Target id="@+id/card_view_item" tag="layout/item_magasin_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="108" endOffset="35"/></Target><Target id="@+id/image_view_item" view="ImageView"><Expressions/><location startLine="20" startOffset="8" endLine="29" endOffset="55"/></Target><Target id="@+id/text_view_nom" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="44" endOffset="43"/></Target><Target id="@+id/text_view_description" view="TextView"><Expressions/><location startLine="47" startOffset="8" endLine="60" endOffset="61"/></Target><Target id="@+id/text_view_categorie" view="TextView"><Expressions/><location startLine="63" startOffset="8" endLine="76" endOffset="35"/></Target><Target id="@+id/text_view_prix" view="TextView"><Expressions/><location startLine="79" startOffset="8" endLine="91" endOffset="33"/></Target><Target id="@+id/ivMenuIndicator" view="ImageView"><Expressions/><location startLine="94" startOffset="8" endLine="104" endOffset="51"/></Target></Targets></Layout>