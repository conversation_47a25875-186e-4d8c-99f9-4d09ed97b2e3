{"logs": [{"outputFile": "com.example.tp2_panier_d_achat.app-mergeDebugResources-51:/values-land/values-land.xml", "map": [{"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\TP2_Panier_D_Achat\\app\\src\\main\\res\\values-land\\dimens.xml", "from": {"startLines": "10,9,6,5,20,19,18,15,14,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "370,325,222,176,766,721,677,583,533,486", "endColumns": "48,44,42,45,44,44,43,45,49,46", "endOffsets": "414,365,260,217,806,761,716,624,578,528"}, "to": {"startLines": "5,6,7,8,27,28,29,30,31,32", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "264,313,358,401,1754,1799,1844,1888,1934,1984", "endColumns": "48,44,42,45,44,44,43,45,49,46", "endOffsets": "308,353,396,442,1794,1839,1883,1929,1979,2026"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5f457b661e995957388e13d6df3e6577\\transformed\\appcompat-1.6.1\\res\\values-land\\values-land.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,125,196", "endColumns": "69,70,67", "endOffsets": "120,191,259"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e49af50215389496670110435eccf411\\transformed\\material-1.8.0\\res\\values-land\\values-land.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,26,35,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,204,277,347,419,489,554,621,691,763,832,901,983,1073,1149,1217,1284,1362,1427,1494,1666,2235,2504", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,25,34,39,42", "endColumns": "74,73,72,69,71,69,64,66,69,71,68,68,81,89,75,67,66,77,64,66,10,10,10,10", "endOffsets": "125,199,272,342,414,484,549,616,686,758,827,896,978,1068,1144,1212,1279,1357,1422,1489,1661,2230,2499,2727"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,33,34,35,39,48,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "447,522,596,669,739,811,881,946,1013,1083,1155,1224,1293,1375,1465,1541,1609,1676,2031,2096,2163,2335,2904,3173", "endLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,33,34,38,47,52,55", "endColumns": "74,73,72,69,71,69,64,66,69,71,68,68,81,89,75,67,66,77,64,66,10,10,10,10", "endOffsets": "517,591,664,734,806,876,941,1008,1078,1150,1219,1288,1370,1460,1536,1604,1671,1749,2091,2158,2330,2899,3168,3396"}}]}]}