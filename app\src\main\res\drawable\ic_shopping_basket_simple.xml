<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="48dp"
    android:height="48dp"
    android:viewportWidth="48"
    android:viewportHeight="48">
    
    <!-- Background circle -->
    <path
        android:fillColor="#424242"
        android:pathData="M24,42 Q32,42 36,38 Q40,34 40,26 Q40,18 36,14 Q32,10 24,10 Q16,10 12,14 Q8,18 8,26 Q8,34 12,38 Q16,42 24,42 Z" />
    
    <!-- Basket base -->
    <path
        android:fillColor="@android:color/transparent"
        android:strokeColor="#FFFFFF"
        android:strokeWidth="2.5"
        android:pathData="M14,26 L34,26 L32,34 L16,34 Z" />
    
    <!-- Basket handles -->
    <path
        android:fillColor="@android:color/transparent"
        android:strokeColor="#FFFFFF"
        android:strokeWidth="2.5"
        android:strokeLineCap="round"
        android:pathData="M14,26 Q12,23 14,20" />
    <path
        android:fillColor="@android:color/transparent"
        android:strokeColor="#FFFFFF"
        android:strokeWidth="2.5"
        android:strokeLineCap="round"
        android:pathData="M34,26 Q36,23 34,20" />
    
    <!-- Simple items in basket -->
    <path
        android:fillColor="@android:color/transparent"
        android:strokeColor="#FFFFFF"
        android:strokeWidth="2"
        android:strokeLineCap="round"
        android:pathData="M18,24 L30,22" />
    <path
        android:fillColor="@android:color/transparent"
        android:strokeColor="#FFFFFF"
        android:strokeWidth="2"
        android:strokeLineCap="round"
        android:pathData="M22,24 L22,18" />
    <path
        android:fillColor="@android:color/transparent"
        android:strokeColor="#FFFFFF"
        android:strokeWidth="2"
        android:pathData="M26,24 L26,19 L29,19 L29,24" />
    
</vector>
