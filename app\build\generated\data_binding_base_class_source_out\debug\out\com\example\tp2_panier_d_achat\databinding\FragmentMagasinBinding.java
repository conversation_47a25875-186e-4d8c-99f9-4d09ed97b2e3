// Generated by view binder compiler. Do not edit!
package com.example.tp2_panier_d_achat.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.tp2_panier_d_achat.R;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentMagasinBinding implements ViewBinding {
  @NonNull
  private final View rootView;

  @NonNull
  public final FloatingActionButton fabAjouterItem;

  @NonNull
  public final ImageView imageViewLogo;

  @NonNull
  public final LinearLayout layoutHeader;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-land/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final LinearLayout layoutListeVide;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout-land/</li>
   * </ul>
   */
  @Nullable
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView recyclerViewMagasin;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout-land/</li>
   * </ul>
   */
  @Nullable
  public final TextView textViewAdminIndicator;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout-land/</li>
   * </ul>
   */
  @Nullable
  public final TextView textViewListeVide;

  @NonNull
  public final TextView textViewTitreMagasin;

  private FragmentMagasinBinding(@NonNull View rootView,
      @NonNull FloatingActionButton fabAjouterItem, @NonNull ImageView imageViewLogo,
      @NonNull LinearLayout layoutHeader, @Nullable LinearLayout layoutListeVide,
      @Nullable ProgressBar progressBar, @NonNull RecyclerView recyclerViewMagasin,
      @Nullable TextView textViewAdminIndicator, @Nullable TextView textViewListeVide,
      @NonNull TextView textViewTitreMagasin) {
    this.rootView = rootView;
    this.fabAjouterItem = fabAjouterItem;
    this.imageViewLogo = imageViewLogo;
    this.layoutHeader = layoutHeader;
    this.layoutListeVide = layoutListeVide;
    this.progressBar = progressBar;
    this.recyclerViewMagasin = recyclerViewMagasin;
    this.textViewAdminIndicator = textViewAdminIndicator;
    this.textViewListeVide = textViewListeVide;
    this.textViewTitreMagasin = textViewTitreMagasin;
  }

  @Override
  @NonNull
  public View getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentMagasinBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentMagasinBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_magasin, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentMagasinBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.fab_ajouter_item;
      FloatingActionButton fabAjouterItem = ViewBindings.findChildViewById(rootView, id);
      if (fabAjouterItem == null) {
        break missingId;
      }

      id = R.id.image_view_logo;
      ImageView imageViewLogo = ViewBindings.findChildViewById(rootView, id);
      if (imageViewLogo == null) {
        break missingId;
      }

      id = R.id.layout_header;
      LinearLayout layoutHeader = ViewBindings.findChildViewById(rootView, id);
      if (layoutHeader == null) {
        break missingId;
      }

      id = R.id.layout_liste_vide;
      LinearLayout layoutListeVide = ViewBindings.findChildViewById(rootView, id);

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);

      id = R.id.recycler_view_magasin;
      RecyclerView recyclerViewMagasin = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewMagasin == null) {
        break missingId;
      }

      id = R.id.text_view_admin_indicator;
      TextView textViewAdminIndicator = ViewBindings.findChildViewById(rootView, id);

      id = R.id.text_view_liste_vide;
      TextView textViewListeVide = ViewBindings.findChildViewById(rootView, id);

      id = R.id.text_view_titre_magasin;
      TextView textViewTitreMagasin = ViewBindings.findChildViewById(rootView, id);
      if (textViewTitreMagasin == null) {
        break missingId;
      }

      return new FragmentMagasinBinding(rootView, fabAjouterItem, imageViewLogo, layoutHeader,
          layoutListeVide, progressBar, recyclerViewMagasin, textViewAdminIndicator,
          textViewListeVide, textViewTitreMagasin);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
