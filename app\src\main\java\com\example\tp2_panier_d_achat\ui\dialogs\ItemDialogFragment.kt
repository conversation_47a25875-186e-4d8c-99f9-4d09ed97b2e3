package com.example.tp2_panier_d_achat.ui.dialogs

import android.app.AlertDialog
import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.widget.ArrayAdapter
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import com.example.tp2_panier_d_achat.R
import com.example.tp2_panier_d_achat.data.Item
import com.example.tp2_panier_d_achat.databinding.DialogItemBinding
import com.example.tp2_panier_d_achat.utils.CategoryIconHelper
import com.example.tp2_panier_d_achat.utils.ValidationUtils

/**
 * DialogFragment pour ajouter ou modifier un item
 */
class ItemDialogFragment : DialogFragment() {

    private var _binding: DialogItemBinding? = null
    private val binding get() = _binding!!
    
    private var onItemSaved: ((Item) -> Unit)? = null
    private var existingItem: Item? = null

    companion object {
        private const val ARG_ITEM = "item"

        fun newInstance(item: Item?, onItemSaved: (Item) -> Unit): ItemDialogFragment {
            return ItemDialogFragment().apply {
                arguments = Bundle().apply {
                    putSerializable(ARG_ITEM, item)
                }
                this.onItemSaved = onItemSaved
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            existingItem = it.getSerializable(ARG_ITEM) as? Item
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        _binding = DialogItemBinding.inflate(LayoutInflater.from(requireContext()))
        
        setupSpinner()
        populateFields()

        val title = if (existingItem == null) {
            getString(R.string.ajouter_item)
        } else {
            getString(R.string.modifier_item)
        }

        return AlertDialog.Builder(requireContext())
            .setTitle(title)
            .setView(binding.root)
            .setPositiveButton(getString(R.string.sauvegarder)) { _, _ ->
                saveItem()
            }
            .setNegativeButton(getString(R.string.annuler), null)
            .create()
    }

    /**
     * Configure le spinner des catégories
     */
    private fun setupSpinner() {
        // Utiliser les catégories du CategoryIconHelper pour avoir toutes les icônes
        val categories = CategoryIconHelper.availableCategories.toTypedArray()

        val adapter = ArrayAdapter(
            requireContext(),
            android.R.layout.simple_spinner_item,
            categories
        )
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.spinnerCategorie.adapter = adapter
    }

    /**
     * Remplit les champs si on modifie un item existant
     */
    private fun populateFields() {
        existingItem?.let { item ->
            binding.editTextNom.setText(item.nom)
            binding.editTextDescription.setText(item.description)
            binding.editTextPrix.setText(item.prix.toString())
            
            // Sélectionner la catégorie appropriée
            val categories = resources.getStringArray(R.array.categories)
            val position = categories.indexOf(item.categorie)
            if (position >= 0) {
                binding.spinnerCategorie.setSelection(position)
            }
        }
    }

    /**
     * Sauvegarde l'item après validation
     */
    private fun saveItem() {
        val nom = binding.editTextNom.text.toString()
        val description = binding.editTextDescription.text.toString()
        val prixText = binding.editTextPrix.text.toString()
        val categoriePosition = binding.spinnerCategorie.selectedItemPosition

        // Mapper la position du spinner à la catégorie
        val categories = arrayOf("fruits", "legumes", "laitier", "viande", "boulangerie", "autre")
        val categorie = if (categoriePosition < categories.size) {
            categories[categoriePosition]
        } else {
            "autre"
        }

        // Validation avec notre utilitaire
        val validationResult = ValidationUtils.validateItemFields(nom, description, prixText, categorie)

        if (!validationResult.isValid) {
            val errorMessage = validationResult.errors.joinToString("\n")
            Toast.makeText(requireContext(), errorMessage, Toast.LENGTH_LONG).show()
            return
        }

        // Créer l'item avec les données formatées
        val item = Item(
            nom = ValidationUtils.formatItemName(nom),
            description = ValidationUtils.formatDescription(description),
            prix = validationResult.validatedPrice!!,
            categorie = categorie
        )

        onItemSaved?.invoke(item)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
