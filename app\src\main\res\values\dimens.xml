<resources>
    <!-- Marges et espacements -->
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>

    <dimen name="margin_small">8dp</dimen>
    <dimen name="margin_medium">16dp</dimen>
    <dimen name="margin_large">24dp</dimen>
    <dimen name="margin_extra_large">32dp</dimen>

    <dimen name="padding_small">8dp</dimen>
    <dimen name="padding_medium">12dp</dimen>
    <dimen name="padding_large">16dp</dimen>
    <dimen name="padding_extra_large">24dp</dimen>

    <!-- Tailles d'éléments -->
    <dimen name="item_image_size">80dp</dimen>
    <dimen name="item_image_small">48dp</dimen>
    <dimen name="icon_size">24dp</dimen>
    <dimen name="icon_size_large">32dp</dimen>
    <dimen name="button_height">48dp</dimen>
    <dimen name="fab_margin">16dp</dimen>

    <!-- Rayons de coins -->
    <dimen name="corner_radius_small">4dp</dimen>
    <dimen name="corner_radius_medium">8dp</dimen>
    <dimen name="corner_radius_large">12dp</dimen>

    <!-- Élévations -->
    <dimen name="elevation_card">4dp</dimen>
    <dimen name="elevation_fab">6dp</dimen>
    <dimen name="elevation_app_bar">4dp</dimen>

    <!-- Tailles de texte -->
    <dimen name="text_size_caption">12sp</dimen>
    <dimen name="text_size_body">14sp</dimen>
    <dimen name="text_size_subtitle">16sp</dimen>
    <dimen name="text_size_title">18sp</dimen>
    <dimen name="text_size_headline">20sp</dimen>
    <dimen name="text_size_display">24sp</dimen>

    <!-- Hauteurs spécifiques -->
    <dimen name="bottom_navigation_height">56dp</dimen>
    <dimen name="toolbar_height">56dp</dimen>
    <dimen name="list_item_height">72dp</dimen>
    <dimen name="list_item_height_small">56dp</dimen>
</resources>