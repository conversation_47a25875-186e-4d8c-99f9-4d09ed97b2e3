<?xml version="1.0" encoding="utf-8"?>
<resources>
    
    <!-- Styles pour les titres -->
    <style name="TitleTextStyle">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">?attr/colorOnBackground</item>
        <item name="android:layout_marginBottom">16dp</item>
    </style>
    
    <style name="SubtitleTextStyle">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">?attr/colorOnSurface</item>
    </style>
    
    <style name="BodyTextStyle">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">?attr/colorOnSurface</item>
    </style>
    
    <style name="CaptionTextStyle">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    
    <!-- Styles pour les prix -->
    <style name="PriceTextStyle">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">?attr/colorPrimary</item>
    </style>
    
    <style name="TotalPriceTextStyle">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">?attr/colorOnPrimary</item>
    </style>
    
    <!-- Styles pour les catégories -->
    <style name="CategoryChipStyle">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:paddingHorizontal">8dp</item>
        <item name="android:paddingVertical">4dp</item>
        <item name="android:background">@drawable/bg_category_chip</item>
    </style>
    
    <!-- Styles pour les boutons -->
    <style name="IconButtonStyle">
        <item name="android:layout_width">48dp</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:background">?attr/selectableItemBackgroundBorderless</item>
        <item name="android:scaleType">centerInside</item>
    </style>
    
    <style name="SmallIconButtonStyle">
        <item name="android:layout_width">32dp</item>
        <item name="android:layout_height">32dp</item>
        <item name="android:background">?attr/selectableItemBackgroundBorderless</item>
        <item name="android:scaleType">centerInside</item>
    </style>
    
    <!-- Styles pour les layouts -->
    <style name="CardContentStyle">
        <item name="android:layout_margin">8dp</item>
        <item name="android:padding">12dp</item>
    </style>
    
    <style name="SectionHeaderStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:padding">16dp</item>
        <item name="android:background">?attr/colorSurface</item>
        <item name="android:elevation">2dp</item>
    </style>
    
    <!-- Styles pour les TextInputLayout -->
    <style name="CustomTextInputLayoutStyle" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">?attr/colorPrimary</item>
        <item name="hintTextColor">?attr/colorPrimary</item>
        <item name="android:layout_marginBottom">8dp</item>
    </style>
    
    <!-- Styles pour les dividers -->
    <style name="DividerStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">1dp</item>
        <item name="android:background">?android:attr/listDivider</item>
        <item name="android:layout_marginVertical">8dp</item>
    </style>
    
</resources>
