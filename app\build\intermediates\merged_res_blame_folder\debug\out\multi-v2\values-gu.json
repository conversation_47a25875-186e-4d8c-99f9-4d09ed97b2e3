{"logs": [{"outputFile": "com.example.tp2_panier_d_achat.app-mergeDebugResources-51:/values-gu/values-gu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e49af50215389496670110435eccf411\\transformed\\material-1.8.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,342,414,496,602,700,799,919,1003,1066,1157,1224,1283,1373,1436,1501,1565,1634,1696,1750,1865,1923,1984,2038,2111,2238,2324,2408,2541,2616,2692,2778,2832,2884,2950,3023,3103,3188,3259,3335,3414,3483,3579,3657,3752,3848,3922,3997,4096,4147,4214,4301,4391,4453,4517,4580,4682,4787,4884,4990,5048,5104", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,73,71,81,105,97,98,119,83,62,90,66,58,89,62,64,63,68,61,53,114,57,60,53,72,126,85,83,132,74,75,85,53,51,65,72,79,84,70,75,78,68,95,77,94,95,73,74,98,50,66,86,89,61,63,62,101,104,96,105,57,55,77", "endOffsets": "263,337,409,491,597,695,794,914,998,1061,1152,1219,1278,1368,1431,1496,1560,1629,1691,1745,1860,1918,1979,2033,2106,2233,2319,2403,2536,2611,2687,2773,2827,2879,2945,3018,3098,3183,3254,3330,3409,3478,3574,3652,3747,3843,3917,3992,4091,4142,4209,4296,4386,4448,4512,4575,4677,4782,4879,4985,5043,5099,5177"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3001,3075,3147,3229,3335,3433,3532,3652,3736,3799,3890,3957,4016,4106,4169,4234,4298,4367,4429,4483,4598,4656,4717,4771,4844,4971,5057,5141,5274,5349,5425,5511,5565,5617,5683,5756,5836,5921,5992,6068,6147,6216,6312,6390,6485,6581,6655,6730,6829,6880,6947,7034,7124,7186,7250,7313,7415,7520,7617,7723,7781,8062", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,96", "endColumns": "12,73,71,81,105,97,98,119,83,62,90,66,58,89,62,64,63,68,61,53,114,57,60,53,72,126,85,83,132,74,75,85,53,51,65,72,79,84,70,75,78,68,95,77,94,95,73,74,98,50,66,86,89,61,63,62,101,104,96,105,57,55,77", "endOffsets": "313,3070,3142,3224,3330,3428,3527,3647,3731,3794,3885,3952,4011,4101,4164,4229,4293,4362,4424,4478,4593,4651,4712,4766,4839,4966,5052,5136,5269,5344,5420,5506,5560,5612,5678,5751,5831,5916,5987,6063,6142,6211,6307,6385,6480,6576,6650,6725,6824,6875,6942,7029,7119,7181,7245,7308,7410,7515,7612,7718,7776,7832,8135"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5f457b661e995957388e13d6df3e6577\\transformed\\appcompat-1.6.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,2864"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,425,529,636,723,823,943,1021,1098,1189,1282,1377,1471,1571,1664,1759,1853,1944,2035,2115,2221,2322,2419,2528,2628,2738,2898,8140", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "420,524,631,718,818,938,1016,1093,1184,1277,1372,1466,1566,1659,1754,1848,1939,2030,2110,2216,2317,2414,2523,2623,2733,2893,2996,8216"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\603bf3700fd8597ee55f4295d905ced2\\transformed\\navigation-ui-2.5.3\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,114", "endOffsets": "160,275"}, "to": {"startLines": "94,95", "startColumns": "4,4", "startOffsets": "7837,7947", "endColumns": "109,114", "endOffsets": "7942,8057"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9850f61f88e3ad6016154401f7e52e36\\transformed\\core-1.9.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "98", "startColumns": "4", "startOffsets": "8221", "endColumns": "100", "endOffsets": "8317"}}]}]}