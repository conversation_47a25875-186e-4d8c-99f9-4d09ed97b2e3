package com.example.tp2_panier_d_achat.ui.magasin

import android.app.AlertDialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.tp2_panier_d_achat.MainActivity
import com.example.tp2_panier_d_achat.R
import com.example.tp2_panier_d_achat.data.Item
import com.example.tp2_panier_d_achat.databinding.FragmentMagasinBinding
import com.example.tp2_panier_d_achat.ui.adapters.MagasinAdapter
import com.example.tp2_panier_d_achat.ui.dialogs.ItemDialogFragment
import com.example.tp2_panier_d_achat.ui.dialogs.QuantiteDialogFragment
import com.example.tp2_panier_d_achat.viewmodel.MagasinViewModel
import com.example.tp2_panier_d_achat.viewmodel.PanierViewModel

/**
 * Fragment affichant la liste des items disponibles en magasin
 * Gère l'affichage, l'ajout au panier, et les fonctionnalités administrateur
 */
class MagasinFragment : Fragment() {

    private var _binding: FragmentMagasinBinding? = null
    private val binding get() = _binding!!

    private lateinit var magasinViewModel: MagasinViewModel
    private lateinit var panierViewModel: PanierViewModel
    private lateinit var adapter: MagasinAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentMagasinBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Récupérer les ViewModels depuis l'activité principale
        val mainActivity = requireActivity() as MainActivity
        magasinViewModel = mainActivity.getMagasinViewModel()
        panierViewModel = mainActivity.getPanierViewModel()

        setupRecyclerView()
        setupFab()
        observeViewModel()
    }

    /**
     * Configure le RecyclerView avec l'adapter
     */
    private fun setupRecyclerView() {
        adapter = MagasinAdapter(
            onItemClick = { item -> onItemClicked(item) },
            onItemLongClick = { item -> onItemLongClicked(item) },
            isAdminMode = { magasinViewModel.isAdminMode.value ?: false }
        )

        binding.recyclerViewMagasin.apply {
            // Utiliser GridLayoutManager en mode paysage, LinearLayoutManager en portrait
            layoutManager = if (resources.configuration.orientation == android.content.res.Configuration.ORIENTATION_LANDSCAPE) {
                GridLayoutManager(requireContext(), 3) // 3 colonnes en paysage
            } else {
                LinearLayoutManager(requireContext()) // 1 colonne en portrait
            }
            adapter = <EMAIL>
        }
    }

    /**
     * Configure le bouton flottant pour ajouter des items
     */
    private fun setupFab() {
        binding.fabAjouterItem.setOnClickListener {
            showItemDialog(null) // null = nouvel item
        }
    }

    /**
     * Observe les changements dans les ViewModels
     */
    private fun observeViewModel() {
        // Observer la liste des items
        magasinViewModel.allItems.observe(viewLifecycleOwner) { items ->
            adapter.submitList(items)
            
            // Afficher/masquer le message de liste vide (si l'élément existe)
            binding.layoutListeVide?.let { listeVide ->
                if (items.isEmpty()) {
                    listeVide.visibility = View.VISIBLE
                    binding.recyclerViewMagasin.visibility = View.GONE
                } else {
                    listeVide.visibility = View.GONE
                    binding.recyclerViewMagasin.visibility = View.VISIBLE
                }
            }
        }

        // Observer le mode administrateur
        magasinViewModel.isAdminMode.observe(viewLifecycleOwner) { isAdmin ->
            // Afficher/masquer le FAB selon le mode admin
            if (isAdmin) {
                binding.fabAjouterItem.show()
                binding.textViewAdminIndicator?.visibility = View.VISIBLE
            } else {
                binding.fabAjouterItem.hide()
                binding.textViewAdminIndicator?.visibility = View.GONE
            }

            // Notifier l'adapter du changement de mode
            adapter.notifyDataSetChanged()
        }

        // Observer l'état de chargement (si l'élément existe)
        magasinViewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar?.visibility = if (isLoading) View.VISIBLE else View.GONE
        }
    }

    /**
     * Gère le clic simple sur un item (ajout au panier)
     */
    private fun onItemClicked(item: Item) {
        // Afficher le dialogue pour choisir la quantité
        val dialog = QuantiteDialogFragment.newInstance(item.nom) { quantite ->
            panierViewModel.ajouterItem(item, quantite)
            Toast.makeText(
                requireContext(),
                "${item.nom} ajouté au panier (${quantite}x)",
                Toast.LENGTH_SHORT
            ).show()
        }
        dialog.show(parentFragmentManager, "QuantiteDialog")
    }

    /**
     * Gère le clic long sur un item (menu contextuel en mode admin)
     */
    private fun onItemLongClicked(item: Item) {
        if (magasinViewModel.isAdminMode.value == true) {
            showContextMenu(item)
        }
    }

    /**
     * Affiche le menu contextuel pour modifier/supprimer un item
     */
    private fun showContextMenu(item: Item) {
        val options = arrayOf(
            getString(R.string.modifier_item),
            getString(R.string.supprimer_item)
        )

        // Créer un AlertDialog avec icônes
        val builder = AlertDialog.Builder(requireContext())
        builder.setTitle("${item.nom} - Actions administrateur")

        // Adapter personnalisé pour afficher les icônes
        val adapter = android.widget.ArrayAdapter<String>(
            requireContext(),
            android.R.layout.select_dialog_item,
            options
        )

        builder.setAdapter(adapter) { _, which ->
            when (which) {
                0 -> showItemDialog(item) // Modifier
                1 -> confirmDeleteItem(item) // Supprimer
            }
        }

        builder.setNegativeButton(getString(R.string.annuler), null)
        builder.show()
    }

    /**
     * Affiche le dialogue pour ajouter/modifier un item
     */
    private fun showItemDialog(item: Item?) {
        val dialog = ItemDialogFragment.newInstance(item) { newItem ->
            if (item == null) {
                // Nouvel item
                magasinViewModel.insertItem(newItem)
            } else {
                // Modification d'un item existant
                magasinViewModel.updateItem(newItem.copy(id = item.id))
            }
        }
        dialog.show(parentFragmentManager, "ItemDialog")
    }

    /**
     * Demande confirmation avant de supprimer un item
     */
    private fun confirmDeleteItem(item: Item) {
        AlertDialog.Builder(requireContext())
            .setTitle("⚠️ ${getString(R.string.supprimer_item)}")
            .setMessage("${getString(R.string.confirmer_suppression)}\n\n📦 Article: ${item.nom}\n💰 Prix: ${String.format("%.2f$", item.prix)}")
            .setPositiveButton("🗑️ ${getString(R.string.oui)}") { _, _ ->
                magasinViewModel.deleteItem(item)
                Toast.makeText(
                    requireContext(),
                    "✅ ${item.nom} supprimé avec succès",
                    Toast.LENGTH_SHORT
                ).show()
            }
            .setNegativeButton("❌ ${getString(R.string.non)}", null)
            .setIcon(android.R.drawable.ic_dialog_alert)
            .show()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
