<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string-array name="categories">
        <item>@string/categorie_fruits</item>
        <item>@string/categorie_legumes</item>
        <item>@string/categorie_laitier</item>
        <item>@string/categorie_viande</item>
        <item>@string/categorie_boulangerie</item>
        <item>@string/categorie_autre</item>
    </string-array>
    <color name="black">#FF000000</color>
    <color name="category_autre">#FF9E9E9E</color>
    <color name="category_boulangerie">#FFFF9800</color>
    <color name="category_fruits">#FFFF6B35</color>
    <color name="category_laitier">#FF2196F3</color>
    <color name="category_legumes">#FF4CAF50</color>
    <color name="category_viande">#FFF44336</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="shopping_accent">#FF2196F3</color>
    <color name="shopping_background">#FFFAFAFA</color>
    <color name="shopping_error">#FFF44336</color>
    <color name="shopping_on_background">#FF212121</color>
    <color name="shopping_on_error">#FFFFFFFF</color>
    <color name="shopping_on_primary">#FFFFFFFF</color>
    <color name="shopping_on_secondary">#FF000000</color>
    <color name="shopping_on_surface">#FF212121</color>
    <color name="shopping_primary">#FF4CAF50</color>
    <color name="shopping_primary_dark">#FF388E3C</color>
    <color name="shopping_primary_light">#FFC8E6C9</color>
    <color name="shopping_secondary">#FFFF9800</color>
    <color name="shopping_secondary_dark">#FFF57C00</color>
    <color name="shopping_secondary_light">#FFFFE0B2</color>
    <color name="shopping_surface">#FFFFFFFF</color>
    <color name="shopping_surface_variant">#FFF5F5F5</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="white">#FFFFFFFF</color>
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="bottom_navigation_height">56dp</dimen>
    <dimen name="button_height">48dp</dimen>
    <dimen name="corner_radius_large">12dp</dimen>
    <dimen name="corner_radius_medium">8dp</dimen>
    <dimen name="corner_radius_small">4dp</dimen>
    <dimen name="elevation_app_bar">4dp</dimen>
    <dimen name="elevation_card">4dp</dimen>
    <dimen name="elevation_fab">6dp</dimen>
    <dimen name="fab_margin">16dp</dimen>
    <dimen name="icon_size">24dp</dimen>
    <dimen name="icon_size_large">32dp</dimen>
    <dimen name="item_image_size">80dp</dimen>
    <dimen name="item_image_small">48dp</dimen>
    <dimen name="list_item_height">72dp</dimen>
    <dimen name="list_item_height_small">56dp</dimen>
    <dimen name="margin_extra_large">32dp</dimen>
    <dimen name="margin_large">24dp</dimen>
    <dimen name="margin_medium">16dp</dimen>
    <dimen name="margin_small">8dp</dimen>
    <dimen name="padding_extra_large">24dp</dimen>
    <dimen name="padding_large">16dp</dimen>
    <dimen name="padding_medium">12dp</dimen>
    <dimen name="padding_small">8dp</dimen>
    <dimen name="text_size_body">14sp</dimen>
    <dimen name="text_size_caption">12sp</dimen>
    <dimen name="text_size_display">24sp</dimen>
    <dimen name="text_size_headline">20sp</dimen>
    <dimen name="text_size_subtitle">16sp</dimen>
    <dimen name="text_size_title">18sp</dimen>
    <dimen name="toolbar_height">56dp</dimen>
    <string name="ajouter_au_panier">Ajouter au panier</string>
    <string name="ajouter_item">Ajouter un article</string>
    <string name="annuler">Annuler</string>
    <string name="app_name">Panier d\'Achat</string>
    <string name="categorie_autre">Autre</string>
    <string name="categorie_boulangerie">Boulangerie</string>
    <string name="categorie_fruits">Fruits</string>
    <string name="categorie_item">Catégorie</string>
    <string name="categorie_laitier">Laitier</string>
    <string name="categorie_legumes">Légumes</string>
    <string name="categorie_viande">Viande</string>
    <string name="choisir_quantite">Choisir la quantité</string>
    <string name="confirmer_suppression">Êtes-vous sûr de vouloir supprimer cet article ?</string>
    <string name="description_item">Description</string>
    <string name="erreur_champs_vides">Veuillez remplir tous les champs</string>
    <string name="erreur_generale">Une erreur s\'est produite</string>
    <string name="erreur_prix_invalide">Prix invalide</string>
    <string name="icone_action">Icône d\'action</string>
    <string name="menu_admin">Admin</string>
    <string name="menu_logout_admin">Quitter mode admin</string>
    <string name="mode_admin_active">Mode administrateur activé</string>
    <string name="mode_admin_desactive">Mode administrateur désactivé</string>
    <string name="modifier_item">Modifier</string>
    <string name="msg_liste_vide">Aucun article disponible</string>
    <string name="nom_item">Nom de l\'article</string>
    <string name="non">Non</string>
    <string name="oui">Oui</string>
    <string name="panier_vide">Votre panier est vide</string>
    <string name="prix_item">Prix ($)</string>
    <string name="quantite">Quantité: %d</string>
    <string name="sauvegarder">Sauvegarder</string>
    <string name="supprimer_item">Supprimer</string>
    <string name="title_magasin">Magasin</string>
    <string name="title_panier">Panier</string>
    <string name="titre_principal">Liste des articles en magasin</string>
    <string name="total_panier">Total: %s</string>
    <string name="valider_commande">Valider la commande</string>
    <string name="vider_panier">Vider le panier</string>
    <style name="BodyTextStyle">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">?attr/colorOnSurface</item>
    </style>
    <style name="CaptionTextStyle">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="CardContentStyle">
        <item name="android:layout_margin">8dp</item>
        <item name="android:padding">12dp</item>
    </style>
    <style name="CategoryChipStyle">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:paddingHorizontal">8dp</item>
        <item name="android:paddingVertical">4dp</item>
        <item name="android:background">@drawable/bg_category_chip</item>
    </style>
    <style name="CustomFABStyle" parent="Widget.MaterialComponents.FloatingActionButton">
        <item name="backgroundTint">?attr/colorSecondary</item>
        <item name="tint">?attr/colorOnSecondary</item>
    </style>
    <style name="CustomTextInputLayoutStyle" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">?attr/colorPrimary</item>
        <item name="hintTextColor">?attr/colorPrimary</item>
        <item name="android:layout_marginBottom">8dp</item>
    </style>
    <style name="DividerStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">1dp</item>
        <item name="android:background">?android:attr/listDivider</item>
        <item name="android:layout_marginVertical">8dp</item>
    </style>
    <style name="IconButtonStyle">
        <item name="android:layout_width">48dp</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:background">?attr/selectableItemBackgroundBorderless</item>
        <item name="android:scaleType">centerInside</item>
    </style>
    <style name="ItemCardStyle" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">8dp</item>
        <item name="cardElevation">4dp</item>
        <item name="cardUseCompatPadding">true</item>
        <item name="contentPadding">12dp</item>
    </style>
    <style name="PriceTextStyle">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">?attr/colorPrimary</item>
    </style>
    <style name="PrimaryButtonStyle" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">?attr/colorOnPrimary</item>
        <item name="backgroundTint">?attr/colorPrimary</item>
        <item name="cornerRadius">8dp</item>
    </style>
    <style name="SecondaryButtonStyle" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:textColor">?attr/colorPrimary</item>
        <item name="strokeColor">?attr/colorPrimary</item>
        <item name="cornerRadius">8dp</item>
    </style>
    <style name="SectionHeaderStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:padding">16dp</item>
        <item name="android:background">?attr/colorSurface</item>
        <item name="android:elevation">2dp</item>
    </style>
    <style name="SmallIconButtonStyle">
        <item name="android:layout_width">32dp</item>
        <item name="android:layout_height">32dp</item>
        <item name="android:background">?attr/selectableItemBackgroundBorderless</item>
        <item name="android:scaleType">centerInside</item>
    </style>
    <style name="SubtitleTextStyle">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">?attr/colorOnSurface</item>
    </style>
    <style name="Theme.TP2_Panier_D_Achat" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/shopping_primary</item>
        <item name="colorPrimaryVariant">@color/shopping_primary_dark</item>
        <item name="colorOnPrimary">@color/shopping_on_primary</item>

        
        <item name="colorSecondary">@color/shopping_secondary</item>
        <item name="colorSecondaryVariant">@color/shopping_secondary_dark</item>
        <item name="colorOnSecondary">@color/shopping_on_secondary</item>

        
        <item name="android:colorBackground">@color/shopping_background</item>
        <item name="colorSurface">@color/shopping_surface</item>
        <item name="colorOnBackground">@color/shopping_on_background</item>
        <item name="colorOnSurface">@color/shopping_on_surface</item>

        
        <item name="colorError">@color/shopping_error</item>
        <item name="colorOnError">@color/shopping_on_error</item>

        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>

        
        <item name="android:windowBackground">@color/shopping_background</item>
    </style>
    <style name="TitleTextStyle">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">?attr/colorOnBackground</item>
        <item name="android:layout_marginBottom">16dp</item>
    </style>
    <style name="TotalPriceTextStyle">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">?attr/colorOnPrimary</item>
    </style>
</resources>