<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_magasin" modulePackage="com.example.tp2_panier_d_achat" filePath="app\src\main\res\layout-land\fragment_magasin.xml" directory="layout-land" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout-land/fragment_magasin_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="109" endOffset="51"/></Target><Target id="@+id/layout_header" view="LinearLayout"><Expressions/><location startLine="9" startOffset="4" endLine="39" endOffset="18"/></Target><Target id="@+id/image_view_logo" view="ImageView"><Expressions/><location startLine="22" startOffset="8" endLine="28" endOffset="49"/></Target><Target id="@+id/text_view_titre_magasin" view="TextView"><Expressions/><location startLine="30" startOffset="8" endLine="37" endOffset="38"/></Target><Target id="@+id/recycler_view_magasin" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="42" startOffset="4" endLine="63" endOffset="47"/></Target><Target id="@+id/fab_ajouter_item" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="66" startOffset="4" endLine="77" endOffset="36"/></Target><Target id="@+id/layout_liste_vide" view="LinearLayout"><Expressions/><location startLine="80" startOffset="4" endLine="107" endOffset="18"/></Target></Targets></Layout>