<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res"/><source path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\build\generated\res\rs\debug"/><source path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res"><file name="bg_admin_indicator" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\bg_admin_indicator.xml" qualifiers="" type="drawable"/><file name="bg_category_chip" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\bg_category_chip.xml" qualifiers="" type="drawable"/><file name="bg_price_chip" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\bg_price_chip.xml" qualifiers="" type="drawable"/><file name="ic_add" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_add.xml" qualifiers="" type="drawable"/><file name="ic_admin" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_admin.xml" qualifiers="" type="drawable"/><file name="ic_app_logo" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_app_logo.xml" qualifiers="" type="drawable"/><file name="ic_bakery" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_bakery.xml" qualifiers="" type="drawable"/><file name="ic_beverage" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_beverage.xml" qualifiers="" type="drawable"/><file name="ic_beverage_simple" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_beverage_simple.xml" qualifiers="" type="drawable"/><file name="ic_boulangerie" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_boulangerie.xml" qualifiers="" type="drawable"/><file name="ic_canned" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_canned.xml" qualifiers="" type="drawable"/><file name="ic_dairy" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_dairy.xml" qualifiers="" type="drawable"/><file name="ic_dairy_simple" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_dairy_simple.xml" qualifiers="" type="drawable"/><file name="ic_dashboard_black_24dp" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_dashboard_black_24dp.xml" qualifiers="" type="drawable"/><file name="ic_default_item" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_default_item.xml" qualifiers="" type="drawable"/><file name="ic_defaut_item" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_defaut_item.xml" qualifiers="" type="drawable"/><file name="ic_defaut_item_background" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_defaut_item_background.xml" qualifiers="" type="drawable"/><file name="ic_delete" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_delete.xml" qualifiers="" type="drawable"/><file name="ic_edit" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_edit.xml" qualifiers="" type="drawable"/><file name="ic_frozen" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_frozen.xml" qualifiers="" type="drawable"/><file name="ic_fruit" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_fruit.xml" qualifiers="" type="drawable"/><file name="ic_fruits" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_fruits.xml" qualifiers="" type="drawable"/><file name="ic_home_black_24dp" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_home_black_24dp.xml" qualifiers="" type="drawable"/><file name="ic_laitier" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_laitier.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_legumes" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_legumes.xml" qualifiers="" type="drawable"/><file name="ic_logout" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_logout.xml" qualifiers="" type="drawable"/><file name="ic_meat" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_meat.xml" qualifiers="" type="drawable"/><file name="ic_more_vert" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_more_vert.xml" qualifiers="" type="drawable"/><file name="ic_notifications_black_24dp" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_notifications_black_24dp.xml" qualifiers="" type="drawable"/><file name="ic_remove" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_remove.xml" qualifiers="" type="drawable"/><file name="ic_shopping_basket" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_shopping_basket.xml" qualifiers="" type="drawable"/><file name="ic_shopping_basket_simple" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_shopping_basket_simple.xml" qualifiers="" type="drawable"/><file name="ic_shopping_cart_empty" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_shopping_cart_empty.xml" qualifiers="" type="drawable"/><file name="ic_vegetable" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_vegetable.xml" qualifiers="" type="drawable"/><file name="ic_viande" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_viande.xml" qualifiers="" type="drawable"/><file name="ic_wine" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\ic_wine.xml" qualifiers="" type="drawable"/><file name="scrollbar_thumb" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\scrollbar_thumb.xml" qualifiers="" type="drawable"/><file name="scrollbar_track" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\drawable\scrollbar_track.xml" qualifiers="" type="drawable"/><file name="activity_main" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="custom_toolbar" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\layout\custom_toolbar.xml" qualifiers="" type="layout"/><file name="dialog_context_menu_item" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\layout\dialog_context_menu_item.xml" qualifiers="" type="layout"/><file name="dialog_item" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\layout\dialog_item.xml" qualifiers="" type="layout"/><file name="fragment_magasin" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\layout\fragment_magasin.xml" qualifiers="" type="layout"/><file name="fragment_panier" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\layout\fragment_panier.xml" qualifiers="" type="layout"/><file name="item_magasin" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\layout\item_magasin.xml" qualifiers="" type="layout"/><file name="item_panier" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\layout\item_panier.xml" qualifiers="" type="layout"/><file name="fragment_magasin" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\layout-land\fragment_magasin.xml" qualifiers="land" type="layout"/><file name="bottom_nav_menu" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\menu\bottom_nav_menu.xml" qualifiers="" type="menu"/><file name="context_menu_item" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\menu\context_menu_item.xml" qualifiers="" type="menu"/><file name="main_menu" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\menu\main_menu.xml" qualifiers="" type="menu"/><file name="defaut_item" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-anydpi-v26\defaut_item.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="defaut_item_round" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-anydpi-v26\defaut_item_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_defaut_item" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-anydpi-v26\ic_defaut_item.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_defaut_item_round" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-anydpi-v26\ic_defaut_item_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="defaut_item" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-hdpi\defaut_item.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="defaut_item_foreground" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-hdpi\defaut_item_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="defaut_item_round" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-hdpi\defaut_item_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_defaut_item" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-hdpi\ic_defaut_item.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_defaut_item_foreground" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-hdpi\ic_defaut_item_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_defaut_item_round" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-hdpi\ic_defaut_item_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="defaut_item" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-mdpi\defaut_item.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="defaut_item_foreground" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-mdpi\defaut_item_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="defaut_item_round" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-mdpi\defaut_item_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_defaut_item" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-mdpi\ic_defaut_item.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_defaut_item_foreground" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-mdpi\ic_defaut_item_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_defaut_item_round" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-mdpi\ic_defaut_item_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="defaut_item" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-xhdpi\defaut_item.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="defaut_item_foreground" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-xhdpi\defaut_item_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="defaut_item_round" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-xhdpi\defaut_item_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_defaut_item" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-xhdpi\ic_defaut_item.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_defaut_item_foreground" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-xhdpi\ic_defaut_item_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_defaut_item_round" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-xhdpi\ic_defaut_item_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="defaut_item" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-xxhdpi\defaut_item.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="defaut_item_foreground" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-xxhdpi\defaut_item_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="defaut_item_round" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-xxhdpi\defaut_item_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_defaut_item" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-xxhdpi\ic_defaut_item.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_defaut_item_foreground" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-xxhdpi\ic_defaut_item_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_defaut_item_round" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-xxhdpi\ic_defaut_item_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="defaut_item" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-xxxhdpi\defaut_item.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="defaut_item_foreground" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-xxxhdpi\defaut_item_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="defaut_item_round" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-xxxhdpi\defaut_item_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_defaut_item" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-xxxhdpi\ic_defaut_item.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_defaut_item_foreground" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-xxxhdpi\ic_defaut_item_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_defaut_item_round" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-xxxhdpi\ic_defaut_item_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="mobile_navigation" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\navigation\mobile_navigation.xml" qualifiers="" type="navigation"/><file path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\values\colors.xml" qualifiers=""><color name="shopping_primary">#FF4CAF50</color><color name="shopping_primary_dark">#FF388E3C</color><color name="shopping_primary_light">#FFC8E6C9</color><color name="shopping_secondary">#FFFF9800</color><color name="shopping_secondary_dark">#FFF57C00</color><color name="shopping_secondary_light">#FFFFE0B2</color><color name="shopping_accent">#FF2196F3</color><color name="shopping_error">#FFF44336</color><color name="shopping_background">#FFFAFAFA</color><color name="shopping_surface">#FFFFFFFF</color><color name="shopping_surface_variant">#FFF5F5F5</color><color name="shopping_on_primary">#FFFFFFFF</color><color name="shopping_on_secondary">#FF000000</color><color name="shopping_on_background">#FF212121</color><color name="shopping_on_surface">#FF212121</color><color name="shopping_on_error">#FFFFFFFF</color><color name="category_fruits">#FFFF6B35</color><color name="category_legumes">#FF4CAF50</color><color name="category_laitier">#FF2196F3</color><color name="category_viande">#FFF44336</color><color name="category_boulangerie">#FFFF9800</color><color name="category_autre">#FF9E9E9E</color><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="activity_horizontal_margin">16dp</dimen><dimen name="activity_vertical_margin">16dp</dimen><dimen name="margin_small">8dp</dimen><dimen name="margin_medium">16dp</dimen><dimen name="margin_large">24dp</dimen><dimen name="margin_extra_large">32dp</dimen><dimen name="padding_small">8dp</dimen><dimen name="padding_medium">12dp</dimen><dimen name="padding_large">16dp</dimen><dimen name="padding_extra_large">24dp</dimen><dimen name="item_image_size">80dp</dimen><dimen name="item_image_small">48dp</dimen><dimen name="icon_size">24dp</dimen><dimen name="icon_size_large">32dp</dimen><dimen name="button_height">48dp</dimen><dimen name="fab_margin">16dp</dimen><dimen name="corner_radius_small">4dp</dimen><dimen name="corner_radius_medium">8dp</dimen><dimen name="corner_radius_large">12dp</dimen><dimen name="elevation_card">4dp</dimen><dimen name="elevation_fab">6dp</dimen><dimen name="elevation_app_bar">4dp</dimen><dimen name="text_size_caption">12sp</dimen><dimen name="text_size_body">14sp</dimen><dimen name="text_size_subtitle">16sp</dimen><dimen name="text_size_title">18sp</dimen><dimen name="text_size_headline">20sp</dimen><dimen name="text_size_display">24sp</dimen><dimen name="bottom_navigation_height">56dp</dimen><dimen name="toolbar_height">56dp</dimen><dimen name="list_item_height">72dp</dimen><dimen name="list_item_height_small">56dp</dimen></file><file path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Panier d\'Achat</string><string name="title_magasin">Magasin</string><string name="title_panier">Panier</string><string name="titre_principal">Liste des articles en magasin</string><string name="msg_liste_vide">Aucun article disponible</string><string name="menu_admin">Admin</string><string name="menu_logout_admin">Quitter mode admin</string><string name="mode_admin_active">Mode administrateur activé</string><string name="mode_admin_desactive">Mode administrateur désactivé</string><string name="ajouter_item">Ajouter un article</string><string name="modifier_item">Modifier</string><string name="supprimer_item">Supprimer</string><string name="confirmer_suppression">Êtes-vous sûr de vouloir supprimer cet article ?</string><string name="oui">Oui</string><string name="non">Non</string><string name="annuler">Annuler</string><string name="icone_action">Icône d\'action</string><string name="total_panier">Total: %s</string><string name="panier_vide">Votre panier est vide</string><string name="quantite">Quantité: %d</string><string name="vider_panier">Vider le panier</string><string name="valider_commande">Valider la commande</string><string name="nom_item">Nom de l\'article</string><string name="description_item">Description</string><string name="prix_item">Prix ($)</string><string name="categorie_item">Catégorie</string><string name="sauvegarder">Sauvegarder</string><string name="choisir_quantite">Choisir la quantité</string><string name="ajouter_au_panier">Ajouter au panier</string><string name="categorie_fruits">Fruits</string><string name="categorie_legumes">Légumes</string><string name="categorie_laitier">Laitier</string><string name="categorie_viande">Viande</string><string name="categorie_boulangerie">Boulangerie</string><string name="categorie_autre">Autre</string><string name="erreur_champs_vides">Veuillez remplir tous les champs</string><string name="erreur_prix_invalide">Prix invalide</string><string name="erreur_generale">Une erreur s\'est produite</string><string-array name="categories">
        <item>@string/categorie_fruits</item>
        <item>@string/categorie_legumes</item>
        <item>@string/categorie_laitier</item>
        <item>@string/categorie_viande</item>
        <item>@string/categorie_boulangerie</item>
        <item>@string/categorie_autre</item>
    </string-array></file><file path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\values\styles.xml" qualifiers=""><style name="TitleTextStyle">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">?attr/colorOnBackground</item>
        <item name="android:layout_marginBottom">16dp</item>
    </style><style name="SubtitleTextStyle">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">?attr/colorOnSurface</item>
    </style><style name="BodyTextStyle">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">?attr/colorOnSurface</item>
    </style><style name="CaptionTextStyle">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style><style name="PriceTextStyle">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">?attr/colorPrimary</item>
    </style><style name="TotalPriceTextStyle">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">?attr/colorOnPrimary</item>
    </style><style name="CategoryChipStyle">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:paddingHorizontal">8dp</item>
        <item name="android:paddingVertical">4dp</item>
        <item name="android:background">@drawable/bg_category_chip</item>
    </style><style name="IconButtonStyle">
        <item name="android:layout_width">48dp</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:background">?attr/selectableItemBackgroundBorderless</item>
        <item name="android:scaleType">centerInside</item>
    </style><style name="SmallIconButtonStyle">
        <item name="android:layout_width">32dp</item>
        <item name="android:layout_height">32dp</item>
        <item name="android:background">?attr/selectableItemBackgroundBorderless</item>
        <item name="android:scaleType">centerInside</item>
    </style><style name="CardContentStyle">
        <item name="android:layout_margin">8dp</item>
        <item name="android:padding">12dp</item>
    </style><style name="SectionHeaderStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:padding">16dp</item>
        <item name="android:background">?attr/colorSurface</item>
        <item name="android:elevation">2dp</item>
    </style><style name="CustomTextInputLayoutStyle" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">?attr/colorPrimary</item>
        <item name="hintTextColor">?attr/colorPrimary</item>
        <item name="android:layout_marginBottom">8dp</item>
    </style><style name="DividerStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">1dp</item>
        <item name="android:background">?android:attr/listDivider</item>
        <item name="android:layout_marginVertical">8dp</item>
    </style></file><file path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.TP2_Panier_D_Achat" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/shopping_primary</item>
        <item name="colorPrimaryVariant">@color/shopping_primary_dark</item>
        <item name="colorOnPrimary">@color/shopping_on_primary</item>

        
        <item name="colorSecondary">@color/shopping_secondary</item>
        <item name="colorSecondaryVariant">@color/shopping_secondary_dark</item>
        <item name="colorOnSecondary">@color/shopping_on_secondary</item>

        
        <item name="android:colorBackground">@color/shopping_background</item>
        <item name="colorSurface">@color/shopping_surface</item>
        <item name="colorOnBackground">@color/shopping_on_background</item>
        <item name="colorOnSurface">@color/shopping_on_surface</item>

        
        <item name="colorError">@color/shopping_error</item>
        <item name="colorOnError">@color/shopping_on_error</item>

        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>

        
        <item name="android:windowBackground">@color/shopping_background</item>
    </style><style name="ItemCardStyle" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">8dp</item>
        <item name="cardElevation">4dp</item>
        <item name="cardUseCompatPadding">true</item>
        <item name="contentPadding">12dp</item>
    </style><style name="PrimaryButtonStyle" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">?attr/colorOnPrimary</item>
        <item name="backgroundTint">?attr/colorPrimary</item>
        <item name="cornerRadius">8dp</item>
    </style><style name="SecondaryButtonStyle" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:textColor">?attr/colorPrimary</item>
        <item name="strokeColor">?attr/colorPrimary</item>
        <item name="cornerRadius">8dp</item>
    </style><style name="CustomFABStyle" parent="Widget.MaterialComponents.FloatingActionButton">
        <item name="backgroundTint">?attr/colorSecondary</item>
        <item name="tint">?attr/colorOnSecondary</item>
    </style></file><file path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\values-land\dimens.xml" qualifiers="land"><dimen name="margin_standard">8dp</dimen><dimen name="margin_small">4dp</dimen><dimen name="card_elevation">2dp</dimen><dimen name="card_corner_radius">6dp</dimen><dimen name="text_size_title">18sp</dimen><dimen name="text_size_subtitle">14sp</dimen><dimen name="text_size_body">12sp</dimen><dimen name="spacing_small">4dp</dimen><dimen name="spacing_medium">8dp</dimen><dimen name="spacing_large">12dp</dimen></file><file path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.TP2_Panier_D_Achat" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/shopping_primary_light</item>
        <item name="colorPrimaryVariant">@color/shopping_primary</item>
        <item name="colorOnPrimary">@color/black</item>

        
        <item name="colorSecondary">@color/shopping_secondary_light</item>
        <item name="colorSecondaryVariant">@color/shopping_secondary</item>
        <item name="colorOnSecondary">@color/black</item>

        
        <item name="android:colorBackground">#FF121212</item>
        <item name="colorSurface">#FF1E1E1E</item>
        <item name="colorOnBackground">@color/white</item>
        <item name="colorOnSurface">@color/white</item>

        
        <item name="colorError">#FFCF6679</item>
        <item name="colorOnError">@color/black</item>

        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>

        
        <item name="android:windowBackground">#FF121212</item>
    </style></file><file name="backup_rules" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source><source path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\build\generated\res\rs\debug"/><source path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>