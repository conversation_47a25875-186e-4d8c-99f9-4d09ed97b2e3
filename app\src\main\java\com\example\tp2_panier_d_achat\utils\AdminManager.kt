package com.example.tp2_panier_d_achat.utils

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData

/**
 * Gestionnaire des fonctionnalités administrateur
 * Centralise la logique de gestion des permissions et actions admin
 */
class AdminManager private constructor(context: Context) {
    
    private val preferencesManager = PreferencesManager.getInstance(context)
    
    private val _isAdminMode = MutableLiveData<Boolean>()
    val isAdminMode: LiveData<Boolean> = _isAdminMode
    
    private val _adminActions = MutableLiveData<AdminAction>()
    val adminActions: LiveData<AdminAction> = _adminActions
    
    companion object {
        @Volatile
        private var INSTANCE: AdminManager? = null
        
        fun getInstance(context: Context): AdminManager {
            return INSTANCE ?: synchronized(this) {
                val instance = AdminManager(context.applicationContext)
                INSTANCE = instance
                instance
            }
        }
    }
    
    init {
        // Initialiser avec l'état sauvegardé
        _isAdminMode.value = preferencesManager.isAdminMode()
    }
    
    /**
     * Active ou désactive le mode administrateur
     */
    fun toggleAdminMode(): Boolean {
        val newState = !(_isAdminMode.value ?: false)
        setAdminMode(newState)
        return newState
    }
    
    /**
     * Définit l'état du mode administrateur
     */
    fun setAdminMode(isAdmin: Boolean) {
        _isAdminMode.value = isAdmin
        preferencesManager.setAdminMode(isAdmin)
        
        // Notifier l'action
        _adminActions.value = if (isAdmin) {
            AdminAction.ADMIN_MODE_ENABLED
        } else {
            AdminAction.ADMIN_MODE_DISABLED
        }
    }
    
    /**
     * Vérifie si une action administrative est autorisée
     */
    fun isActionAllowed(action: AdminAction): Boolean {
        return when (action) {
            AdminAction.ADD_ITEM,
            AdminAction.EDIT_ITEM,
            AdminAction.DELETE_ITEM,
            AdminAction.VIEW_CONTEXT_MENU -> _isAdminMode.value ?: false
            
            AdminAction.VIEW_ITEMS,
            AdminAction.ADD_TO_CART -> true // Toujours autorisé
            
            else -> false
        }
    }
    
    /**
     * Exécute une action administrative si autorisée
     */
    fun executeAction(action: AdminAction, onSuccess: () -> Unit, onDenied: () -> Unit = {}) {
        if (isActionAllowed(action)) {
            onSuccess()
            _adminActions.value = action
        } else {
            onDenied()
        }
    }
    
    /**
     * Obtient le message approprié pour une action
     */
    fun getActionMessage(context: Context, action: AdminAction): String {
        return when (action) {
            AdminAction.ADMIN_MODE_ENABLED -> "Mode administrateur activé"
            AdminAction.ADMIN_MODE_DISABLED -> "Mode administrateur désactivé"
            AdminAction.ADD_ITEM -> "Article ajouté avec succès"
            AdminAction.EDIT_ITEM -> "Article modifié avec succès"
            AdminAction.DELETE_ITEM -> "Article supprimé avec succès"
            AdminAction.ACTION_DENIED -> "Action non autorisée - Mode administrateur requis"
            else -> ""
        }
    }
    
    /**
     * Réinitialise le gestionnaire admin
     */
    fun reset() {
        setAdminMode(false)
        _adminActions.value = AdminAction.ADMIN_MODE_DISABLED
    }
    
    /**
     * Obtient des statistiques d'utilisation admin
     */
    fun getAdminStats(): AdminStats {
        return AdminStats(
            isCurrentlyAdmin = _isAdminMode.value ?: false,
            lastAction = _adminActions.value
        )
    }
    
    /**
     * Énumération des actions administrateur
     */
    enum class AdminAction {
        ADMIN_MODE_ENABLED,
        ADMIN_MODE_DISABLED,
        ADD_ITEM,
        EDIT_ITEM,
        DELETE_ITEM,
        VIEW_CONTEXT_MENU,
        VIEW_ITEMS,
        ADD_TO_CART,
        ACTION_DENIED
    }
    
    /**
     * Classe de données pour les statistiques admin
     */
    data class AdminStats(
        val isCurrentlyAdmin: Boolean,
        val lastAction: AdminAction?
    )
}
