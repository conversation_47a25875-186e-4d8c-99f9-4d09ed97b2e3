+com.example.tp2_panier_d_achat.MainActivity/com.example.tp2_panier_d_achat.data.AppDatabaseJcom.example.tp2_panier_d_achat.data.AppDatabase.Companion.DatabaseCallback(com.example.tp2_panier_d_achat.data.Item9com.example.tp2_panier_d_achat.ui.adapters.MagasinAdapterHcom.example.tp2_panier_d_achat.ui.adapters.MagasinAdapter.ItemViewHolderJcom.example.tp2_panier_d_achat.ui.adapters.MagasinAdapter.ItemDiffCallback8com.example.tp2_panier_d_achat.ui.adapters.PanierAdapterIcom.example.tp2_panier_d_achat.ui.adapters.PanierAdapter.PanierViewHolderOcom.example.tp2_panier_d_achat.ui.adapters.PanierAdapter.PanierItemDiffCallback;com.example.tp2_panier_d_achat.ui.adapters.ViewPagerAdapter<<EMAIL>.tp2_panier_d_achat.ui.dialogs.QuantiteDialogFragment9com.example.tp2_panier_d_achat.ui.magasin.MagasinFragment7com.example.tp2_panier_d_achat.ui.panier.PanierFragment=com.example.tp2_panier_d_achat.utils.AdminManager.AdminAction9com.example.tp2_panier_d_achat.viewmodel.MagasinViewModel8com.example.tp2_panier_d_achat.viewmodel.PanierViewModel=com.example.tp2_panier_d_achat.databinding.ItemMagasinBinding<com.example.tp2_panier_d_achat.databinding.DialogItemBinding<<EMAIL>.tp2_panier_d_achat.databinding.FragmentPanierBinding>com.example.tp2_panier_d_achat.databinding.ActivityMainBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        