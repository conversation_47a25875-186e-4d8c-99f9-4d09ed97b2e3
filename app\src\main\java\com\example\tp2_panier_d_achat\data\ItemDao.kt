package com.example.tp2_panier_d_achat.data

import androidx.lifecycle.LiveData
import androidx.room.*

/**
 * DAO (Data Access Object) pour la gestion des items dans la base de données
 * Définit toutes les opérations CRUD pour les items
 */
@Dao
interface ItemDao {
    
    /**
     * Récupère tous les items de la base de données
     * Retourne un LiveData pour observer les changements automatiquement
     */
    @Query("SELECT * FROM items ORDER BY nom ASC")
    fun getAllItems(): LiveData<List<Item>>
    
    /**
     * Récupère un item par son ID
     */
    @Query("SELECT * FROM items WHERE id = :id")
    suspend fun getItemById(id: Long): Item?
    
    /**
     * Récupère tous les items d'une catégorie spécifique
     */
    @Query("SELECT * FROM items WHERE categorie = :categorie ORDER BY nom ASC")
    suspend fun getItemsByCategorie(categorie: String): List<Item>
    
    /**
     * Insère un nouvel item dans la base de données
     * Retourne l'ID de l'item inséré
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertItem(item: Item): Long
    
    /**
     * Met à jour un item existant
     */
    @Update
    suspend fun updateItem(item: Item)
    
    /**
     * Supprime un item de la base de données
     */
    @Delete
    suspend fun deleteItem(item: Item)
    
    /**
     * Supprime tous les items de la base de données
     */
    @Query("DELETE FROM items")
    suspend fun deleteAllItems()
    
    /**
     * Compte le nombre total d'items
     */
    @Query("SELECT COUNT(*) FROM items")
    suspend fun getItemCount(): Int
}
