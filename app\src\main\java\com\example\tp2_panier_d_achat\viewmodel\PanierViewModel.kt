package com.example.tp2_panier_d_achat.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.example.tp2_panier_d_achat.data.Item
import com.example.tp2_panier_d_achat.data.PanierItem
import com.example.tp2_panier_d_achat.utils.TaxCalculator

/**
 * ViewModel pour le fragment Panier
 * Gère la liste des items sélectionnés et le calcul du total
 */
class PanierViewModel : ViewModel() {
    
    /**
     * Liste privée des items dans le panier
     */
    private val _panierItems = MutableLiveData<MutableList<PanierItem>>(mutableListOf())
    val panierItems: LiveData<MutableList<PanierItem>> = _panierItems
    
    /**
     * Sous-total du panier (avant taxes)
     */
    private val _sousTotal = MutableLiveData<Double>(0.0)
    val sousTotal: LiveData<Double> = _sousTotal

    /**
     * Montant de la taxe
     */
    private val _taxe = MutableLiveData<Double>(0.0)
    val taxe: LiveData<Double> = _taxe

    /**
     * Total du panier calculé dynamiquement (avec taxes)
     */
    private val _totalPanier = MutableLiveData<Double>(0.0)
    val totalPanier: LiveData<Double> = _totalPanier

    /**
     * Nombre total d'items dans le panier
     */
    private val _nombreItems = MutableLiveData<Int>(0)
    val nombreItems: LiveData<Int> = _nombreItems
    
    /**
     * Ajoute un item au panier ou augmente sa quantité s'il existe déjà
     */
    fun ajouterItem(item: Item, quantite: Int = 1) {
        val currentList = _panierItems.value ?: mutableListOf()
        
        // Vérifier si l'item existe déjà dans le panier
        val existingItem = currentList.find { it.item.id == item.id }
        
        if (existingItem != null) {
            // Augmenter la quantité de l'item existant
            existingItem.quantite += quantite
        } else {
            // Ajouter un nouvel item au panier
            currentList.add(PanierItem(item, quantite))
        }
        
        _panierItems.value = currentList
        calculerTotal()
        calculerNombreItems()
    }
    
    /**
     * Modifie la quantité d'un item dans le panier
     */
    fun modifierQuantite(itemId: Long, nouvelleQuantite: Int) {
        val currentList = _panierItems.value?.toMutableList() ?: mutableListOf()
        val itemIndex = currentList.indexOfFirst { it.item.id == itemId }

        if (itemIndex != -1) {
            if (nouvelleQuantite <= 0) {
                // Supprimer l'item si la quantité est 0 ou négative
                currentList.removeAt(itemIndex)
            } else {
                // Créer un nouvel objet PanierItem avec la nouvelle quantité
                val oldItem = currentList[itemIndex]
                val newItem = PanierItem(oldItem.item, nouvelleQuantite)
                currentList[itemIndex] = newItem
            }

            _panierItems.value = currentList
            calculerTotal()
            calculerNombreItems()
        }
    }
    
    /**
     * Supprime un item du panier
     */
    fun supprimerItem(itemId: Long) {
        val currentList = _panierItems.value ?: mutableListOf()
        currentList.removeAll { it.item.id == itemId }
        
        _panierItems.value = currentList
        calculerTotal()
        calculerNombreItems()
    }
    
    /**
     * Vide complètement le panier
     */
    fun viderPanier() {
        _panierItems.value = mutableListOf()
        _totalPanier.value = 0.0
        _nombreItems.value = 0
    }
    
    /**
     * Calcule le total du panier avec taxes
     */
    private fun calculerTotal() {
        val subtotal = _panierItems.value?.sumOf { it.getPrixTotal() } ?: 0.0
        val taxCalculation = TaxCalculator.calculateDetailed(subtotal)

        _sousTotal.value = taxCalculation.subtotal
        _taxe.value = taxCalculation.taxAmount
        _totalPanier.value = taxCalculation.total
    }

    /**
     * Obtient les détails de calcul de taxe
     */
    fun getTaxCalculation(): TaxCalculator.TaxCalculation {
        val subtotal = _sousTotal.value ?: 0.0
        return TaxCalculator.calculateDetailed(subtotal)
    }
    
    /**
     * Calcule le nombre total d'items dans le panier
     */
    private fun calculerNombreItems() {
        val nombre = _panierItems.value?.sumOf { it.quantite } ?: 0
        _nombreItems.value = nombre
    }
    
    /**
     * Retourne le total formaté en string
     */
    fun getTotalFormate(): String {
        return String.format("%.2f$", _totalPanier.value ?: 0.0)
    }
    
    /**
     * Vérifie si le panier est vide
     */
    fun isPanierVide(): Boolean {
        return _panierItems.value?.isEmpty() ?: true
    }
}
