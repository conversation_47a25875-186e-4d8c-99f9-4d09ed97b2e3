-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:2:1-27:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:2:1-27:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:2:1-27:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:2:1-27:12
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\260884c0a1f86db2ad85df736fa46d2f\transformed\viewbinding-7.4.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\d4a999ad4c86334000ed3373fa8bb6d7\transformed\navigation-ui-ktx-2.5.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-ui:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\603bf3700fd8597ee55f4295d905ced2\transformed\navigation-ui-2.5.3\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.material:material:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\e49af50215389496670110435eccf411\transformed\material-1.8.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\76a7832dc45b8b1214c08752a79198f8\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a50352f8ea90c8f7e1486bc170598b2a\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\5f457b661e995957388e13d6df3e6577\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\a4ad96b745ce283e735b51a9e2ee052e\transformed\navigation-fragment-ktx-2.5.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-fragment:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\db160b95f116c21f7525addf222adafd\transformed\navigation-fragment-2.5.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.5.7] C:\Users\<USER>\.gradle\caches\transforms-3\b366340a9f36ca906742d3b1934d7e2e\transformed\fragment-ktx-1.5.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dfe7093b73f6665005796d2a42d7cf0b\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\transforms-3\33b4704e1351f2d704933c6449b1fe83\transformed\fragment-1.5.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed96b09fcf93b379b26a7427194b22a2\transformed\activity-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\d24eb333bb4ca87c64736213294c7d9f\transformed\navigation-runtime-ktx-2.5.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-runtime:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\a81d7c2dd4e15e7ca29600d4ab253ac1\transformed\navigation-runtime-2.5.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity-ktx:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\d2f762768ea58201fa2684477774825a\transformed\activity-ktx-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e54e517efbf903c0267d2105e0f5085\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\729c6a4d2da094711eb7f2ef40fd6c8f\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.navigation:navigation-common-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\2522b588b1304facc27d1eec6a9ae280\transformed\navigation-common-ktx-2.5.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-common:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\5d6f7350745658ee25b6f5fedc29ad6c\transformed\navigation-common-2.5.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\c766da6088e92a223ec9db8fbe59c7d7\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\4988ce7b98da470eb92f0123a9c3a321\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\8e69fe915bd06a2a15982c2936174ade\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b35ba037e1244952bde8e4c1c5359aa\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\fc60634c1d43c0a083c667d8a118d479\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\77fe1f8ca8d28f84e074cb37472f7fac\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1cce4250f2104db0c945ac67905d3da8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f2f09b4a1abe71467190369b6526f4d\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4645a2bf3354a7ec6de909d1ae3c66a8\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\a45cd692e0c33c470849affcfa6c2644\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\bff4eeb5a77521e9d837a85d9b773b8b\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\18782e0a21bf1012045f6069ae514895\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\63da9e43113aed5e48273903a2b204f9\transformed\core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\85c8a9dca901a187f060df210d23be0d\transformed\recyclerview-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\40463457e0b8f1c31b9abc1af921cfcc\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8d29f5c6bf2dbfbbc7e4dbbae2423a5\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f6774feefbf334576cef1034443c613c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\165e81bba1a973786ab62dee6000a4bc\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\46baca419b14aef09d9976bdb379b26a\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0d92461001775e4e8f037762e39f3794\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1efd2501fc7e3b07d60208611cc5ba0\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\0d7fad7276acbf12f47993caecdb4502\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\256f6a6b293e0af0350a35948f95cbd9\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9850f61f88e3ad6016154401f7e52e36\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\c03955e6713aaf4c9832dba5d59956c4\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\ad586db14b65ad59f75bc225be3ad6b0\transformed\lifecycle-livedata-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\b1d4aba857664a7587019060fe8b34b7\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-ktx:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\ef46529bdc1f289afde14c218c10ede3\transformed\room-ktx-2.4.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c6eb3a7f9a5aea60146e759f475b8658\transformed\room-runtime-2.4.3\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0fc87b178bae2168817d7165738a96aa\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc90fb9569df0b50f4bee1022b0cecb7\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\027e5b0346abb58c51d48c810892217b\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\61085e94dc65d5deafbeb9325fff59d7\transformed\sqlite-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3042d0451400b7e1a25cff459165c3a0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b78f2173cca2550efc4a6a1a8bc4c24\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\55a312da890f9f0e2b15ae326a5b2a0f\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e5065476caa9e5398795a33609f649c\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\302581ebc68477fc3956043144bcce65\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d415fdf0d49370b639eb4e7046cebfe5\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56dbc3d5ede0c008e998a02de496f746\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb957e70d33c1bc43fa91e889b7abb1a\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\63b6a451df4865ff0851d25ead76fb3f\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:2:1-27:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:2:1-27:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:2:1-27:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:2:1-27:12
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:2:1-27:12
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:2:1-27:12
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:5:5-25:19
MERGED from [com.google.android.material:material:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\e49af50215389496670110435eccf411\transformed\material-1.8.0\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\e49af50215389496670110435eccf411\transformed\material-1.8.0\AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\76a7832dc45b8b1214c08752a79198f8\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\76a7832dc45b8b1214c08752a79198f8\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\729c6a4d2da094711eb7f2ef40fd6c8f\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\729c6a4d2da094711eb7f2ef40fd6c8f\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\8e69fe915bd06a2a15982c2936174ade\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\8e69fe915bd06a2a15982c2936174ade\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\256f6a6b293e0af0350a35948f95cbd9\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\256f6a6b293e0af0350a35948f95cbd9\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9850f61f88e3ad6016154401f7e52e36\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9850f61f88e3ad6016154401f7e52e36\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c6eb3a7f9a5aea60146e759f475b8658\transformed\room-runtime-2.4.3\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c6eb3a7f9a5aea60146e759f475b8658\transformed\room-runtime-2.4.3\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3042d0451400b7e1a25cff459165c3a0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3042d0451400b7e1a25cff459165c3a0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b78f2173cca2550efc4a6a1a8bc4c24\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b78f2173cca2550efc4a6a1a8bc4c24\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9850f61f88e3ad6016154401f7e52e36\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:12:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:10:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:8:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:11:9-54
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:9:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:6:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:13:9-56
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:7:9-65
activity#com.example.tp2_panier_d_achat.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:14:9-24:20
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:17:13-45
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:16:13-36
	android:configChanges
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:18:13-74
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:15:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:19:13-23:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:20:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:20:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:22:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:22:27-74
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\260884c0a1f86db2ad85df736fa46d2f\transformed\viewbinding-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\260884c0a1f86db2ad85df736fa46d2f\transformed\viewbinding-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\d4a999ad4c86334000ed3373fa8bb6d7\transformed\navigation-ui-ktx-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-ui-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\d4a999ad4c86334000ed3373fa8bb6d7\transformed\navigation-ui-ktx-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-ui:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\603bf3700fd8597ee55f4295d905ced2\transformed\navigation-ui-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-ui:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\603bf3700fd8597ee55f4295d905ced2\transformed\navigation-ui-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\e49af50215389496670110435eccf411\transformed\material-1.8.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\e49af50215389496670110435eccf411\transformed\material-1.8.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\76a7832dc45b8b1214c08752a79198f8\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\76a7832dc45b8b1214c08752a79198f8\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a50352f8ea90c8f7e1486bc170598b2a\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a50352f8ea90c8f7e1486bc170598b2a\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\5f457b661e995957388e13d6df3e6577\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\5f457b661e995957388e13d6df3e6577\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\a4ad96b745ce283e735b51a9e2ee052e\transformed\navigation-fragment-ktx-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-fragment-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\a4ad96b745ce283e735b51a9e2ee052e\transformed\navigation-fragment-ktx-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-fragment:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\db160b95f116c21f7525addf222adafd\transformed\navigation-fragment-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-fragment:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\db160b95f116c21f7525addf222adafd\transformed\navigation-fragment-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.5.7] C:\Users\<USER>\.gradle\caches\transforms-3\b366340a9f36ca906742d3b1934d7e2e\transformed\fragment-ktx-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.5.7] C:\Users\<USER>\.gradle\caches\transforms-3\b366340a9f36ca906742d3b1934d7e2e\transformed\fragment-ktx-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dfe7093b73f6665005796d2a42d7cf0b\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dfe7093b73f6665005796d2a42d7cf0b\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\transforms-3\33b4704e1351f2d704933c6449b1fe83\transformed\fragment-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\transforms-3\33b4704e1351f2d704933c6449b1fe83\transformed\fragment-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed96b09fcf93b379b26a7427194b22a2\transformed\activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed96b09fcf93b379b26a7427194b22a2\transformed\activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\d24eb333bb4ca87c64736213294c7d9f\transformed\navigation-runtime-ktx-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-runtime-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\d24eb333bb4ca87c64736213294c7d9f\transformed\navigation-runtime-ktx-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-runtime:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\a81d7c2dd4e15e7ca29600d4ab253ac1\transformed\navigation-runtime-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-runtime:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\a81d7c2dd4e15e7ca29600d4ab253ac1\transformed\navigation-runtime-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity-ktx:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\d2f762768ea58201fa2684477774825a\transformed\activity-ktx-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\d2f762768ea58201fa2684477774825a\transformed\activity-ktx-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e54e517efbf903c0267d2105e0f5085\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e54e517efbf903c0267d2105e0f5085\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\729c6a4d2da094711eb7f2ef40fd6c8f\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\729c6a4d2da094711eb7f2ef40fd6c8f\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\2522b588b1304facc27d1eec6a9ae280\transformed\navigation-common-ktx-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common-ktx:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\2522b588b1304facc27d1eec6a9ae280\transformed\navigation-common-ktx-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\5d6f7350745658ee25b6f5fedc29ad6c\transformed\navigation-common-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common:2.5.3] C:\Users\<USER>\.gradle\caches\transforms-3\5d6f7350745658ee25b6f5fedc29ad6c\transformed\navigation-common-2.5.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\c766da6088e92a223ec9db8fbe59c7d7\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\c766da6088e92a223ec9db8fbe59c7d7\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\4988ce7b98da470eb92f0123a9c3a321\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\4988ce7b98da470eb92f0123a9c3a321\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\8e69fe915bd06a2a15982c2936174ade\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\8e69fe915bd06a2a15982c2936174ade\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b35ba037e1244952bde8e4c1c5359aa\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3b35ba037e1244952bde8e4c1c5359aa\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\fc60634c1d43c0a083c667d8a118d479\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\fc60634c1d43c0a083c667d8a118d479\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\77fe1f8ca8d28f84e074cb37472f7fac\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\77fe1f8ca8d28f84e074cb37472f7fac\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1cce4250f2104db0c945ac67905d3da8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1cce4250f2104db0c945ac67905d3da8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f2f09b4a1abe71467190369b6526f4d\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f2f09b4a1abe71467190369b6526f4d\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4645a2bf3354a7ec6de909d1ae3c66a8\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4645a2bf3354a7ec6de909d1ae3c66a8\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\a45cd692e0c33c470849affcfa6c2644\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\a45cd692e0c33c470849affcfa6c2644\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\bff4eeb5a77521e9d837a85d9b773b8b\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\bff4eeb5a77521e9d837a85d9b773b8b\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\18782e0a21bf1012045f6069ae514895\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\18782e0a21bf1012045f6069ae514895\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\63da9e43113aed5e48273903a2b204f9\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\63da9e43113aed5e48273903a2b204f9\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\85c8a9dca901a187f060df210d23be0d\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\85c8a9dca901a187f060df210d23be0d\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\40463457e0b8f1c31b9abc1af921cfcc\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\40463457e0b8f1c31b9abc1af921cfcc\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8d29f5c6bf2dbfbbc7e4dbbae2423a5\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8d29f5c6bf2dbfbbc7e4dbbae2423a5\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f6774feefbf334576cef1034443c613c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f6774feefbf334576cef1034443c613c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\165e81bba1a973786ab62dee6000a4bc\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\165e81bba1a973786ab62dee6000a4bc\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\46baca419b14aef09d9976bdb379b26a\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\46baca419b14aef09d9976bdb379b26a\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0d92461001775e4e8f037762e39f3794\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0d92461001775e4e8f037762e39f3794\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1efd2501fc7e3b07d60208611cc5ba0\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1efd2501fc7e3b07d60208611cc5ba0\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\0d7fad7276acbf12f47993caecdb4502\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\0d7fad7276acbf12f47993caecdb4502\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\256f6a6b293e0af0350a35948f95cbd9\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\256f6a6b293e0af0350a35948f95cbd9\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9850f61f88e3ad6016154401f7e52e36\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9850f61f88e3ad6016154401f7e52e36\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\c03955e6713aaf4c9832dba5d59956c4\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\c03955e6713aaf4c9832dba5d59956c4\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\ad586db14b65ad59f75bc225be3ad6b0\transformed\lifecycle-livedata-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\ad586db14b65ad59f75bc225be3ad6b0\transformed\lifecycle-livedata-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\b1d4aba857664a7587019060fe8b34b7\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\b1d4aba857664a7587019060fe8b34b7\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\ef46529bdc1f289afde14c218c10ede3\transformed\room-ktx-2.4.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-ktx:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\ef46529bdc1f289afde14c218c10ede3\transformed\room-ktx-2.4.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c6eb3a7f9a5aea60146e759f475b8658\transformed\room-runtime-2.4.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c6eb3a7f9a5aea60146e759f475b8658\transformed\room-runtime-2.4.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0fc87b178bae2168817d7165738a96aa\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0fc87b178bae2168817d7165738a96aa\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc90fb9569df0b50f4bee1022b0cecb7\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc90fb9569df0b50f4bee1022b0cecb7\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\027e5b0346abb58c51d48c810892217b\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\027e5b0346abb58c51d48c810892217b\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\61085e94dc65d5deafbeb9325fff59d7\transformed\sqlite-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\61085e94dc65d5deafbeb9325fff59d7\transformed\sqlite-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3042d0451400b7e1a25cff459165c3a0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3042d0451400b7e1a25cff459165c3a0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b78f2173cca2550efc4a6a1a8bc4c24\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b78f2173cca2550efc4a6a1a8bc4c24\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\55a312da890f9f0e2b15ae326a5b2a0f\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\55a312da890f9f0e2b15ae326a5b2a0f\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e5065476caa9e5398795a33609f649c\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e5065476caa9e5398795a33609f649c\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\302581ebc68477fc3956043144bcce65\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\302581ebc68477fc3956043144bcce65\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d415fdf0d49370b639eb4e7046cebfe5\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d415fdf0d49370b639eb4e7046cebfe5\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56dbc3d5ede0c008e998a02de496f746\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\56dbc3d5ede0c008e998a02de496f746\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb957e70d33c1bc43fa91e889b7abb1a\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb957e70d33c1bc43fa91e889b7abb1a\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\63b6a451df4865ff0851d25ead76fb3f\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\63b6a451df4865ff0851d25ead76fb3f\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
INJECTED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\729c6a4d2da094711eb7f2ef40fd6c8f\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\8e69fe915bd06a2a15982c2936174ade\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\8e69fe915bd06a2a15982c2936174ade\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b78f2173cca2550efc4a6a1a8bc4c24\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b78f2173cca2550efc4a6a1a8bc4c24\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\729c6a4d2da094711eb7f2ef40fd6c8f\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\729c6a4d2da094711eb7f2ef40fd6c8f\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\729c6a4d2da094711eb7f2ef40fd6c8f\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\729c6a4d2da094711eb7f2ef40fd6c8f\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\729c6a4d2da094711eb7f2ef40fd6c8f\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\729c6a4d2da094711eb7f2ef40fd6c8f\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\729c6a4d2da094711eb7f2ef40fd6c8f\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\8e69fe915bd06a2a15982c2936174ade\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\8e69fe915bd06a2a15982c2936174ade\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\8e69fe915bd06a2a15982c2936174ade\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\256f6a6b293e0af0350a35948f95cbd9\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\256f6a6b293e0af0350a35948f95cbd9\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\256f6a6b293e0af0350a35948f95cbd9\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\256f6a6b293e0af0350a35948f95cbd9\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\256f6a6b293e0af0350a35948f95cbd9\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\256f6a6b293e0af0350a35948f95cbd9\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9850f61f88e3ad6016154401f7e52e36\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9850f61f88e3ad6016154401f7e52e36\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9850f61f88e3ad6016154401f7e52e36\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.example.tp2_panier_d_achat.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9850f61f88e3ad6016154401f7e52e36\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9850f61f88e3ad6016154401f7e52e36\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9850f61f88e3ad6016154401f7e52e36\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9850f61f88e3ad6016154401f7e52e36\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9850f61f88e3ad6016154401f7e52e36\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.tp2_panier_d_achat.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9850f61f88e3ad6016154401f7e52e36\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9850f61f88e3ad6016154401f7e52e36\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c6eb3a7f9a5aea60146e759f475b8658\transformed\room-runtime-2.4.3\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c6eb3a7f9a5aea60146e759f475b8658\transformed\room-runtime-2.4.3\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c6eb3a7f9a5aea60146e759f475b8658\transformed\room-runtime-2.4.3\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c6eb3a7f9a5aea60146e759f475b8658\transformed\room-runtime-2.4.3\AndroidManifest.xml:26:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
