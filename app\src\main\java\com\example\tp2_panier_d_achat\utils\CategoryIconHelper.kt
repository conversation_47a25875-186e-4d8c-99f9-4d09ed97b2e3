package com.example.tp2_panier_d_achat.utils

import com.example.tp2_panier_d_achat.R

/**
 * Utilitaire pour gérer les icônes selon les catégories d'articles
 */
object CategoryIconHelper {
    
    /**
     * Retourne l'ID de la ressource drawable selon la catégorie
     * @param categorie La catégorie de l'article
     * @return L'ID de la ressource drawable correspondante
     */
    fun getIconResource(categorie: String): Int {
        return when (categorie.lowercase().trim()) {
            "fruits", "fruit" -> R.drawable.ic_fruit
            "legumes", "légumes", "legume", "légume", "vegetable", "vegetables" -> R.drawable.ic_vegetable
            "laitier", "laitiers", "dairy", "lait", "fromage" -> R.drawable.ic_dairy
            "viande", "viandes", "meat", "boeuf", "porc", "poulet", "agneau" -> R.drawable.ic_meat
            "boulangerie", "pain", "bread", "bakery" -> R.drawable.ic_bakery
            "boisson", "boissons", "drink", "drinks", "beverage" -> R.drawable.ic_beverage
            "vin", "vins", "wine", "alcool", "alcohol", "biere", "bière", "beer", "champagne", "whiskey", "vodka", "rhum", "spiritueux" -> R.drawable.ic_wine
            "conserve", "conserves", "canned", "can" -> R.drawable.ic_canned
            "surgele", "surgelé", "surgelés", "frozen" -> R.drawable.ic_frozen
            "epicerie", "grocery", "autre", "other" -> R.drawable.ic_shopping_basket
            else -> R.drawable.ic_shopping_basket
        }
    }
    
    /**
     * Retourne une description de l'icône pour l'accessibilité
     * @param categorie La catégorie de l'article
     * @return Description textuelle de l'icône
     */
    fun getIconDescription(categorie: String): String {
        return when (categorie.lowercase().trim()) {
            "fruits", "fruit" -> "Icône fruit"
            "legumes", "légumes", "legume", "légume", "vegetable", "vegetables" -> "Icône légume"
            "laitier", "laitiers", "dairy", "lait", "fromage" -> "Icône produit laitier"
            "viande", "viandes", "meat", "boeuf", "porc", "poulet", "agneau" -> "Icône viande"
            "boulangerie", "pain", "bread", "bakery" -> "Icône boulangerie"
            "boisson", "boissons", "drink", "drinks", "beverage" -> "Icône boisson"
            "vin", "vins", "wine", "alcool", "alcohol", "biere", "bière", "beer", "champagne", "whiskey", "vodka", "rhum", "spiritueux" -> "Icône vinoiserie"
            "conserve", "conserves", "canned", "can" -> "Icône conserve"
            "surgele", "surgelé", "surgelés", "frozen" -> "Icône surgelé"
            "epicerie", "grocery", "autre", "other" -> "Icône panier épicerie"
            else -> "Icône panier article"
        }
    }
    
    /**
     * Liste des catégories disponibles pour l'interface admin
     */
    val availableCategories = listOf(
        "Fruits",
        "Légumes",
        "Viande",
        "Laitier",
        "Boulangerie",
        "Boissons",
        "Vin",
        "Conserves",
        "Surgelés",
        "Épicerie"
    )
}
