<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Thème principal de l'application Panier d'Achat -->
    <style name="Theme.TP2_Panier_D_Achat" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Couleurs principales -->
        <item name="colorPrimary">@color/shopping_primary</item>
        <item name="colorPrimaryVariant">@color/shopping_primary_dark</item>
        <item name="colorOnPrimary">@color/shopping_on_primary</item>

        <!-- Couleurs secondaires -->
        <item name="colorSecondary">@color/shopping_secondary</item>
        <item name="colorSecondaryVariant">@color/shopping_secondary_dark</item>
        <item name="colorOnSecondary">@color/shopping_on_secondary</item>

        <!-- Couleurs de surface et fond -->
        <item name="android:colorBackground">@color/shopping_background</item>
        <item name="colorSurface">@color/shopping_surface</item>
        <item name="colorOnBackground">@color/shopping_on_background</item>
        <item name="colorOnSurface">@color/shopping_on_surface</item>

        <!-- Couleur d'erreur -->
        <item name="colorError">@color/shopping_error</item>
        <item name="colorOnError">@color/shopping_on_error</item>

        <!-- Barre de statut -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>

        <!-- Personnalisations supplémentaires -->
        <item name="android:windowBackground">@color/shopping_background</item>
    </style>

    <!-- Style pour les cartes d'items -->
    <style name="ItemCardStyle" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">8dp</item>
        <item name="cardElevation">4dp</item>
        <item name="cardUseCompatPadding">true</item>
        <item name="contentPadding">12dp</item>
    </style>

    <!-- Style pour les boutons principaux -->
    <style name="PrimaryButtonStyle" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">?attr/colorOnPrimary</item>
        <item name="backgroundTint">?attr/colorPrimary</item>
        <item name="cornerRadius">8dp</item>
    </style>

    <!-- Style pour les boutons secondaires -->
    <style name="SecondaryButtonStyle" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:textColor">?attr/colorPrimary</item>
        <item name="strokeColor">?attr/colorPrimary</item>
        <item name="cornerRadius">8dp</item>
    </style>

    <!-- Style pour les FAB -->
    <style name="CustomFABStyle" parent="Widget.MaterialComponents.FloatingActionButton">
        <item name="backgroundTint">?attr/colorSecondary</item>
        <item name="tint">?attr/colorOnSecondary</item>
    </style>
</resources>