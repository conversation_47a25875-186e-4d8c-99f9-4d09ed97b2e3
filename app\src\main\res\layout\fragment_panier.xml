<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.panier.PanierFragment">

    <TextView
        android:id="@+id/text_view_titre_panier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="Mon Panier"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Layout pour le contenu du panier (visible quand il y a des items) -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_panier_content"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/text_view_titre_panier">

        <!-- Informations sur le nombre d'items -->
        <TextView
            android:id="@+id/text_view_nombre_items"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:textColor="?android:attr/textColorSecondary"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="3 articles" />

        <!-- RecyclerView pour afficher la liste des items du panier -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view_panier"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="8dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="8dp"
            android:layout_marginBottom="8dp"
            android:clipToPadding="false"
            android:paddingBottom="8dp"
            android:scrollbars="vertical"
            android:scrollbarStyle="outsideOverlay"
            android:scrollbarThumbVertical="@drawable/scrollbar_thumb"
            android:scrollbarTrackVertical="@drawable/scrollbar_track"
            android:fadeScrollbars="false"
            android:overScrollMode="ifContentScrolls"
            android:nestedScrollingEnabled="true"
            app:layout_constraintBottom_toTopOf="@+id/layout_total"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/text_view_nombre_items"
            tools:listitem="@layout/item_panier" />

        <!-- Layout pour le total et les actions -->
        <LinearLayout
            android:id="@+id/layout_total"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="?attr/colorPrimary"
            android:elevation="4dp"
            android:orientation="vertical"
            android:padding="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <!-- Sous-total -->
            <TextView
                android:id="@+id/text_view_sous_total"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp"
                android:gravity="end"
                android:text="Sous-total: 0.00$"
                android:textColor="@android:color/white"
                android:textSize="14sp"
                tools:text="Sous-total: 22.10$" />

            <!-- Taxe -->
            <TextView
                android:id="@+id/text_view_taxe"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:gravity="end"
                android:text="Taxe (15%): 0.00$"
                android:textColor="@android:color/white"
                android:textSize="14sp"
                tools:text="Taxe (15%): 3.32$" />

            <!-- Total final -->
            <TextView
                android:id="@+id/text_view_total"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:gravity="center"
                android:text="@string/total_panier"
                android:textColor="@android:color/white"
                android:textSize="18sp"
                android:textStyle="bold"
                tools:text="Total: 25.42$" />

            <Button
                android:id="@+id/button_valider_commande"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:backgroundTint="?attr/colorPrimary"
                android:text="@string/valider_commande"
                android:textColor="@android:color/white" />

            <Button
                android:id="@+id/button_vider_panier"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:backgroundTint="?attr/colorError"
                android:text="@string/vider_panier"
                android:textColor="@android:color/white" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Layout pour panier vide (visible quand le panier est vide) -->
    <LinearLayout
        android:id="@+id/layout_panier_vide"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/text_view_titre_panier">

        <ImageView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginBottom="16dp"
            android:alpha="0.5"
            android:src="@drawable/ic_shopping_cart_empty"
            app:tint="?android:attr/textColorSecondary" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/panier_vide"
            android:textColor="?android:attr/textColorSecondary"
            android:textSize="16sp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>