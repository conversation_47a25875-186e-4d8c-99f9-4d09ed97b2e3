package com.example.tp2_panier_d_achat.data

/**
 * Classe représentant un item dans le panier d'achat
 * Contient l'item original et la quantité sélectionnée par l'utilisateur
 */
data class PanierItem(
    /**
     * L'item original du magasin
     */
    val item: Item,
    
    /**
     * Quantité sélectionnée par l'utilisateur
     */
    var quantite: Int = 1
) {
    /**
     * Calcule le prix total pour cet item (prix unitaire × quantité)
     */
    fun getPrixTotal(): Double {
        return item.prix * quantite
    }
    
    /**
     * Retourne une description formatée pour l'affichage
     */
    fun getDescriptionFormatee(): String {
        return "${item.nom} (${quantite}x) - ${String.format("%.2f", getPrixTotal())}$"
    }
}
