<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- Nom de l'item -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:hint="@string/nom_item">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/edit_text_nom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textCapWords"
            android:maxLines="1" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Description de l'item -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:hint="@string/description_item">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/edit_text_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textMultiLine"
            android:maxLines="3" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Prix de l'item -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:hint="@string/prix_item">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/edit_text_prix"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="numberDecimal"
            android:maxLines="1" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Catégorie de l'item -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="4dp"
        android:text="@string/categorie_item"
        android:textSize="16sp" />

    <Spinner
        android:id="@+id/spinner_categorie"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp" />

</LinearLayout>
