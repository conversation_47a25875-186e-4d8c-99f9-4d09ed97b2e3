package com.example.tp2_panier_d_achat.utils

import android.content.Context
import android.content.SharedPreferences

/**
 * Gestionnaire des préférences de l'application
 * Gère la persistance des paramètres utilisateur
 */
class PreferencesManager(context: Context) {
    
    private val sharedPreferences: SharedPreferences = context.getSharedPreferences(
        PREFS_NAME, Context.MODE_PRIVATE
    )
    
    companion object {
        private const val PREFS_NAME = "panier_achat_prefs"
        private const val KEY_ADMIN_MODE = "admin_mode"
        private const val KEY_FIRST_LAUNCH = "first_launch"
        private const val KEY_LAST_TOTAL = "last_total"
        
        @Volatile
        private var INSTANCE: PreferencesManager? = null
        
        fun getInstance(context: Context): PreferencesManager {
            return INSTANCE ?: synchronized(this) {
                val instance = PreferencesManager(context.applicationContext)
                INSTANCE = instance
                instance
            }
        }
    }
    
    /**
     * Sauvegarde l'état du mode administrateur
     */
    fun setAdminMode(isAdmin: Boolean) {
        sharedPreferences.edit()
            .putBoolean(KEY_ADMIN_MODE, isAdmin)
            .apply()
    }
    
    /**
     * Récupère l'état du mode administrateur
     */
    fun isAdminMode(): Boolean {
        return sharedPreferences.getBoolean(KEY_ADMIN_MODE, false)
    }
    
    /**
     * Marque que l'application a été lancée au moins une fois
     */
    fun setFirstLaunchCompleted() {
        sharedPreferences.edit()
            .putBoolean(KEY_FIRST_LAUNCH, false)
            .apply()
    }
    
    /**
     * Vérifie si c'est le premier lancement de l'application
     */
    fun isFirstLaunch(): Boolean {
        return sharedPreferences.getBoolean(KEY_FIRST_LAUNCH, true)
    }
    
    /**
     * Sauvegarde le dernier total du panier
     */
    fun saveLastTotal(total: Double) {
        sharedPreferences.edit()
            .putFloat(KEY_LAST_TOTAL, total.toFloat())
            .apply()
    }
    
    /**
     * Récupère le dernier total du panier
     */
    fun getLastTotal(): Double {
        return sharedPreferences.getFloat(KEY_LAST_TOTAL, 0f).toDouble()
    }
    
    /**
     * Efface toutes les préférences
     */
    fun clearAll() {
        sharedPreferences.edit().clear().apply()
    }
}
