{"logs": [{"outputFile": "com.example.tp2_panier_d_achat.app-mergeDebugResources-51:/values-ja/values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\603bf3700fd8597ee55f4295d905ced2\\transformed\\navigation-ui-2.5.3\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,154", "endColumns": "98,109", "endOffsets": "149,259"}, "to": {"startLines": "94,95", "startColumns": "4,4", "startOffsets": "7198,7297", "endColumns": "98,109", "endOffsets": "7292,7402"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5f457b661e995957388e13d6df3e6577\\transformed\\appcompat-1.6.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,2691", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,2765"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "303,400,493,598,680,778,886,964,1039,1130,1223,1318,1412,1512,1605,1700,1794,1885,1976,2054,2156,2254,2349,2452,2548,2644,2792,7480", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "395,488,593,675,773,881,959,1034,1125,1218,1313,1407,1507,1600,1695,1789,1880,1971,2049,2151,2249,2344,2447,2543,2639,2787,2884,7554"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9850f61f88e3ad6016154401f7e52e36\\transformed\\core-1.9.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "98", "startColumns": "4", "startOffsets": "7559", "endColumns": "100", "endOffsets": "7655"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e49af50215389496670110435eccf411\\transformed\\material-1.8.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,253,320,384,453,534,616,701,805,881,944,1028,1092,1150,1231,1292,1356,1411,1470,1527,1581,1674,1730,1787,1841,1907,2007,2083,2164,2286,2348,2410,2489,2542,2593,2659,2729,2799,2876,2940,3011,3079,3142,3221,3284,3364,3446,3518,3589,3661,3709,3773,3848,3925,3987,4051,4114,4200,4284,4365,4450,4507,4562", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,66,63,68,80,81,84,103,75,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,80,121,61,61,78,52,50,65,69,69,76,63,70,67,62,78,62,79,81,71,70,71,47,63,74,76,61,63,62,85,83,80,84,56,54,72", "endOffsets": "248,315,379,448,529,611,696,800,876,939,1023,1087,1145,1226,1287,1351,1406,1465,1522,1576,1669,1725,1782,1836,1902,2002,2078,2159,2281,2343,2405,2484,2537,2588,2654,2724,2794,2871,2935,3006,3074,3137,3216,3279,3359,3441,3513,3584,3656,3704,3768,3843,3920,3982,4046,4109,4195,4279,4360,4445,4502,4557,4630"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2889,2956,3020,3089,3170,3252,3337,3441,3517,3580,3664,3728,3786,3867,3928,3992,4047,4106,4163,4217,4310,4366,4423,4477,4543,4643,4719,4800,4922,4984,5046,5125,5178,5229,5295,5365,5435,5512,5576,5647,5715,5778,5857,5920,6000,6082,6154,6225,6297,6345,6409,6484,6561,6623,6687,6750,6836,6920,7001,7086,7143,7407", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,96", "endColumns": "12,66,63,68,80,81,84,103,75,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,80,121,61,61,78,52,50,65,69,69,76,63,70,67,62,78,62,79,81,71,70,71,47,63,74,76,61,63,62,85,83,80,84,56,54,72", "endOffsets": "298,2951,3015,3084,3165,3247,3332,3436,3512,3575,3659,3723,3781,3862,3923,3987,4042,4101,4158,4212,4305,4361,4418,4472,4538,4638,4714,4795,4917,4979,5041,5120,5173,5224,5290,5360,5430,5507,5571,5642,5710,5773,5852,5915,5995,6077,6149,6220,6292,6340,6404,6479,6556,6618,6682,6745,6831,6915,6996,7081,7138,7193,7475"}}]}]}