package com.example.tp2_panier_d_achat.data

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.sqlite.db.SupportSQLiteDatabase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * Base de données Room pour l'application
 * Gère la création et la configuration de la base de données SQLite
 */
@Database(
    entities = [Item::class],
    version = 1,
    exportSchema = false
)
abstract class AppDatabase : RoomDatabase() {
    
    /**
     * Retourne le DAO pour les items
     */
    abstract fun itemDao(): ItemDao
    
    companion object {
        @Volatile
        private var INSTANCE: AppDatabase? = null
        
        /**
         * Retourne l'instance unique de la base de données (Singleton)
         */
        fun getDatabase(context: Context): AppDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AppDatabase::class.java,
                    "panier_database"
                )
                .addCallback(DatabaseCallback())
                .build()
                INSTANCE = instance
                instance
            }
        }
        
        /**
         * Callback pour initialiser la base de données avec des données de test
         */
        private class DatabaseCallback : RoomDatabase.Callback() {
            override fun onCreate(db: SupportSQLiteDatabase) {
                super.onCreate(db)
                INSTANCE?.let { database ->
                    CoroutineScope(Dispatchers.IO).launch {
                        populateDatabase(database.itemDao())
                    }
                }
            }
        }
        
        /**
         * Peuple la base de données avec des items de démonstration
         */
        private suspend fun populateDatabase(itemDao: ItemDao) {
            // Supprimer tout contenu existant
            itemDao.deleteAllItems()
            
            // Ajouter des items de démonstration
            val items = listOf(
                // Fruits
                Item(nom = "Pommes", description = "Pommes rouges fraîches", prix = 2.99, categorie = "fruits"),
                Item(nom = "Bananes", description = "Bananes mûres", prix = 1.49, categorie = "fruits"),
                Item(nom = "Oranges", description = "Oranges juteuses", prix = 3.49, categorie = "fruits"),

                // Légumes
                Item(nom = "Carottes", description = "Carottes biologiques", prix = 1.99, categorie = "legumes"),
                Item(nom = "Tomates", description = "Tomates cerises", prix = 3.49, categorie = "legumes"),
                Item(nom = "Brocolis", description = "Brocolis frais", prix = 2.79, categorie = "legumes"),

                // Viandes
                Item(nom = "Poulet", description = "Poitrine de poulet", prix = 8.99, categorie = "viande"),
                Item(nom = "Bœuf", description = "Steak de bœuf AAA", prix = 12.99, categorie = "viande"),
                Item(nom = "Porc", description = "Côtelettes de porc", prix = 7.49, categorie = "viande"),
                Item(nom = "Agneau", description = "Gigot d'agneau", prix = 15.99, categorie = "viande"),
                Item(nom = "Saucisses", description = "Saucisses italiennes", prix = 5.99, categorie = "viande"),

                // Laitier
                Item(nom = "Lait", description = "Lait 2% 1L", prix = 3.29, categorie = "laitier"),
                Item(nom = "Fromage", description = "Fromage cheddar", prix = 4.99, categorie = "laitier"),
                Item(nom = "Yogourt", description = "Yogourt grec nature", prix = 4.49, categorie = "laitier"),

                // Boulangerie
                Item(nom = "Pain", description = "Pain blanc tranché", prix = 2.49, categorie = "boulangerie"),
                Item(nom = "Croissants", description = "Croissants au beurre", prix = 3.99, categorie = "boulangerie")
            )
            
            items.forEach { item ->
                itemDao.insertItem(item)
            }
        }
    }
}
