package com.example.tp2_panier_d_achat.ui.panier

import android.app.AlertDialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.tp2_panier_d_achat.MainActivity
import com.example.tp2_panier_d_achat.R
import com.example.tp2_panier_d_achat.databinding.FragmentPanierBinding
import com.example.tp2_panier_d_achat.ui.adapters.PanierAdapter
import com.example.tp2_panier_d_achat.viewmodel.PanierViewModel

/**
 * Fragment affichant le panier d'achat avec les items sélectionnés
 * Gère l'affichage des items, les quantités et le calcul du total
 */
class PanierFragment : Fragment() {

    private var _binding: FragmentPanierBinding? = null
    private val binding get() = _binding!!

    private lateinit var panierViewModel: PanierViewModel
    private lateinit var adapter: PanierAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPanierBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Récupérer le ViewModel depuis l'activité principale
        val mainActivity = requireActivity() as MainActivity
        panierViewModel = mainActivity.getPanierViewModel()

        setupRecyclerView()
        setupButtons()
        observeViewModel()
    }

    /**
     * Configure le RecyclerView avec l'adapter
     */
    private fun setupRecyclerView() {
        adapter = PanierAdapter(
            onQuantiteChanged = { itemId, nouvelleQuantite ->
                panierViewModel.modifierQuantite(itemId, nouvelleQuantite)
            },
            onItemRemoved = { itemId ->
                // Afficher une boîte de dialogue de confirmation
                androidx.appcompat.app.AlertDialog.Builder(requireContext())
                    .setTitle("Confirmer la suppression")
                    .setMessage("Voulez-vous vraiment supprimer cet article du panier ?")
                    .setPositiveButton("Supprimer") { _, _ ->
                        panierViewModel.supprimerItem(itemId)
                        Toast.makeText(
                            requireContext(),
                            "Article supprimé du panier",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                    .setNegativeButton("Annuler", null)
                    .show()
            }
        )

        binding.recyclerViewPanier.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = <EMAIL>
        }
    }

    /**
     * Configure les boutons du fragment
     */
    private fun setupButtons() {
        binding.buttonViderPanier.setOnClickListener {
            if (!panierViewModel.isPanierVide()) {
                showConfirmViderPanier()
            }
        }

        binding.buttonValiderCommande.setOnClickListener {
            if (!panierViewModel.isPanierVide()) {
                showConfirmValiderCommande()
            } else {
                Toast.makeText(
                    requireContext(),
                    "Votre panier est vide",
                    Toast.LENGTH_SHORT
                ).show()
            }
        }
    }

    /**
     * Observe les changements dans le ViewModel
     */
    private fun observeViewModel() {
        // Observer la liste des items du panier
        panierViewModel.panierItems.observe(viewLifecycleOwner) { items ->
            adapter.submitList(items.toList()) // Convertir en List immutable
            
            // Afficher/masquer les vues selon l'état du panier
            if (items.isEmpty()) {
                binding.layoutPanierVide.visibility = View.VISIBLE
                binding.layoutPanierContent.visibility = View.GONE
            } else {
                binding.layoutPanierVide.visibility = View.GONE
                binding.layoutPanierContent.visibility = View.VISIBLE
            }
        }

        // Observer le sous-total
        panierViewModel.sousTotal.observe(viewLifecycleOwner) { sousTotal ->
            binding.textViewSousTotal.text = "Sous-total: ${String.format("%.2f", sousTotal)}$"
        }

        // Observer la taxe
        panierViewModel.taxe.observe(viewLifecycleOwner) { taxe ->
            binding.textViewTaxe.text = "Taxe (15%): ${String.format("%.2f", taxe)}$"
        }

        // Observer le total du panier
        panierViewModel.totalPanier.observe(viewLifecycleOwner) { total ->
            binding.textViewTotal.text = getString(R.string.total_panier, String.format("%.2f$", total))
        }

        // Observer le nombre d'items
        panierViewModel.nombreItems.observe(viewLifecycleOwner) { nombre ->
            binding.textViewNombreItems.text = if (nombre == 1) {
                "$nombre article"
            } else {
                "$nombre articles"
            }
        }
    }

    /**
     * Affiche une confirmation avant de vider le panier
     */
    private fun showConfirmViderPanier() {
        AlertDialog.Builder(requireContext())
            .setTitle(getString(R.string.vider_panier))
            .setMessage("Êtes-vous sûr de vouloir vider votre panier ?")
            .setPositiveButton(getString(R.string.oui)) { _, _ ->
                panierViewModel.viderPanier()
                Toast.makeText(
                    requireContext(),
                    "Panier vidé",
                    Toast.LENGTH_SHORT
                ).show()
            }
            .setNegativeButton(getString(R.string.non), null)
            .show()
    }

    /**
     * Affiche une boîte de dialogue de confirmation pour valider la commande
     */
    private fun showConfirmValiderCommande() {
        val taxCalculation = panierViewModel.getTaxCalculation()
        val nombreItems = panierViewModel.nombreItems.value ?: 0

        val message = """
            Résumé de la commande:

            Articles: $nombreItems
            Sous-total: ${taxCalculation.getFormattedSubtotal()}$
            Taxe (${taxCalculation.getFormattedTaxRate()}): ${taxCalculation.getFormattedTax()}$

            Total: ${taxCalculation.getFormattedTotal()}$

            Confirmer cette commande ?
        """.trimIndent()

        AlertDialog.Builder(requireContext())
            .setTitle("Valider la commande")
            .setMessage(message)
            .setPositiveButton("Confirmer") { _, _ ->
                // Simuler la validation de commande
                panierViewModel.viderPanier()
                Toast.makeText(
                    requireContext(),
                    "Commande validée avec succès ! Merci pour votre achat.",
                    Toast.LENGTH_LONG
                ).show()
            }
            .setNegativeButton("Annuler", null)
            .show()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
