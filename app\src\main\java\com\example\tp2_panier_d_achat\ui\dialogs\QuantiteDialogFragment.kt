package com.example.tp2_panier_d_achat.ui.dialogs

import android.app.AlertDialog
import android.app.Dialog
import android.os.Bundle
import android.widget.NumberPicker
import androidx.fragment.app.DialogFragment
import com.example.tp2_panier_d_achat.R

/**
 * DialogFragment pour choisir la quantité d'un item à ajouter au panier
 */
class QuantiteDialogFragment : DialogFragment() {

    private var onQuantiteSelected: ((Int) -> Unit)? = null
    private var itemNom: String = ""

    companion object {
        private const val ARG_ITEM_NOM = "item_nom"

        fun newInstance(itemNom: String, onQuantiteSelected: (Int) -> Unit): QuantiteDialogFragment {
            return QuantiteDialogFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_ITEM_NOM, itemNom)
                }
                this.onQuantiteSelected = onQuantiteSelected
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            itemNom = it.getString(ARG_ITEM_NOM, "")
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val numberPicker = NumberPicker(requireContext()).apply {
            minValue = 1
            maxValue = 20
            value = 1
            wrapSelectorWheel = false
        }

        return AlertDialog.Builder(requireContext())
            .setTitle(getString(R.string.choisir_quantite))
            .setMessage("$itemNom")
            .setView(numberPicker)
            .setPositiveButton(getString(R.string.ajouter_au_panier)) { _, _ ->
                onQuantiteSelected?.invoke(numberPicker.value)
            }
            .setNegativeButton(getString(R.string.annuler), null)
            .create()
    }
}
