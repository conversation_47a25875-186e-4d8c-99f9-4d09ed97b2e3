{"logs": [{"outputFile": "com.example.tp2_panier_d_achat.app-mergeDebugResources-51:/values-cs/values-cs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5f457b661e995957388e13d6df3e6577\\transformed\\appcompat-1.6.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,2799", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,2877"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "418,525,627,737,823,928,1045,1123,1199,1290,1383,1478,1572,1666,1759,1854,1951,2042,2133,2217,2321,2433,2532,2638,2749,2851,3014,8245", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "520,622,732,818,923,1040,1118,1194,1285,1378,1473,1567,1661,1754,1849,1946,2037,2128,2212,2316,2428,2527,2633,2744,2846,3009,3107,8323"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e49af50215389496670110435eccf411\\transformed\\material-1.8.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,368,446,524,601,704,796,888,1014,1095,1160,1259,1335,1396,1485,1549,1616,1670,1738,1798,1852,1969,2029,2091,2145,2217,2339,2423,2515,2652,2730,2812,2900,2954,3005,3071,3143,3220,3304,3376,3453,3527,3598,3686,3757,3850,3945,4019,4093,4189,4241,4308,4394,4482,4544,4608,4671,4781,4877,4976,5074,5132,5187", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "endColumns": "12,77,77,76,102,91,91,125,80,64,98,75,60,88,63,66,53,67,59,53,116,59,61,53,71,121,83,91,136,77,81,87,53,50,65,71,76,83,71,76,73,70,87,70,92,94,73,73,95,51,66,85,87,61,63,62,109,95,98,97,57,54,78", "endOffsets": "363,441,519,596,699,791,883,1009,1090,1155,1254,1330,1391,1480,1544,1611,1665,1733,1793,1847,1964,2024,2086,2140,2212,2334,2418,2510,2647,2725,2807,2895,2949,3000,3066,3138,3215,3299,3371,3448,3522,3593,3681,3752,3845,3940,4014,4088,4184,4236,4303,4389,4477,4539,4603,4666,4776,4872,4971,5069,5127,5182,5261"}, "to": {"startLines": "2,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3112,3190,3268,3345,3448,3540,3632,3758,3839,3904,4003,4079,4140,4229,4293,4360,4414,4482,4542,4596,4713,4773,4835,4889,4961,5083,5167,5259,5396,5474,5556,5644,5698,5749,5815,5887,5964,6048,6120,6197,6271,6342,6430,6501,6594,6689,6763,6837,6933,6985,7052,7138,7226,7288,7352,7415,7525,7621,7720,7818,7876,8166", "endLines": "7,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,98", "endColumns": "12,77,77,76,102,91,91,125,80,64,98,75,60,88,63,66,53,67,59,53,116,59,61,53,71,121,83,91,136,77,81,87,53,50,65,71,76,83,71,76,73,70,87,70,92,94,73,73,95,51,66,85,87,61,63,62,109,95,98,97,57,54,78", "endOffsets": "413,3185,3263,3340,3443,3535,3627,3753,3834,3899,3998,4074,4135,4224,4288,4355,4409,4477,4537,4591,4708,4768,4830,4884,4956,5078,5162,5254,5391,5469,5551,5639,5693,5744,5810,5882,5959,6043,6115,6192,6266,6337,6425,6496,6589,6684,6758,6832,6928,6980,7047,7133,7221,7283,7347,7410,7520,7616,7715,7813,7871,7926,8240"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\603bf3700fd8597ee55f4295d905ced2\\transformed\\navigation-ui-2.5.3\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,126", "endOffsets": "158,285"}, "to": {"startLines": "96,97", "startColumns": "4,4", "startOffsets": "7931,8039", "endColumns": "107,126", "endOffsets": "8034,8161"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9850f61f88e3ad6016154401f7e52e36\\transformed\\core-1.9.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "100", "startColumns": "4", "startOffsets": "8328", "endColumns": "100", "endOffsets": "8424"}}]}]}