/ Header Record For PersistentHashMapValueStorage) (androidx.appcompat.app.AppCompatActivity androidx.room.RoomDatabase$ #androidx.room.RoomDatabase.Callback java.io.Serializable) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback1 0androidx.viewpager2.adapter.FragmentStateAdapter% $androidx.fragment.app.DialogFragment% $androidx.fragment.app.DialogFragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment kotlin.Enum$ #androidx.lifecycle.AndroidViewModel androidx.lifecycle.ViewModel!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding