// Generated by view binder compiler. Do not edit!
package com.example.tp2_panier_d_achat.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.tp2_panier_d_achat.R;
import com.google.android.material.appbar.MaterialToolbar;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class CustomToolbarBinding implements ViewBinding {
  @NonNull
  private final MaterialToolbar rootView;

  @NonNull
  public final MaterialToolbar toolbar;

  @NonNull
  public final TextView toolbarAdminIndicator;

  @NonNull
  public final ImageView toolbarLogo;

  @NonNull
  public final TextView toolbarTitle;

  private CustomToolbarBinding(@NonNull MaterialToolbar rootView, @NonNull MaterialToolbar toolbar,
      @NonNull TextView toolbarAdminIndicator, @NonNull ImageView toolbarLogo,
      @NonNull TextView toolbarTitle) {
    this.rootView = rootView;
    this.toolbar = toolbar;
    this.toolbarAdminIndicator = toolbarAdminIndicator;
    this.toolbarLogo = toolbarLogo;
    this.toolbarTitle = toolbarTitle;
  }

  @Override
  @NonNull
  public MaterialToolbar getRoot() {
    return rootView;
  }

  @NonNull
  public static CustomToolbarBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static CustomToolbarBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.custom_toolbar, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static CustomToolbarBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      MaterialToolbar toolbar = (MaterialToolbar) rootView;

      id = R.id.toolbar_admin_indicator;
      TextView toolbarAdminIndicator = ViewBindings.findChildViewById(rootView, id);
      if (toolbarAdminIndicator == null) {
        break missingId;
      }

      id = R.id.toolbar_logo;
      ImageView toolbarLogo = ViewBindings.findChildViewById(rootView, id);
      if (toolbarLogo == null) {
        break missingId;
      }

      id = R.id.toolbar_title;
      TextView toolbarTitle = ViewBindings.findChildViewById(rootView, id);
      if (toolbarTitle == null) {
        break missingId;
      }

      return new CustomToolbarBinding((MaterialToolbar) rootView, toolbar, toolbarAdminIndicator,
          toolbarLogo, toolbarTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
