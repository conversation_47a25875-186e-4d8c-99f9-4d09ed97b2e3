<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_magasin" modulePackage="com.example.tp2_panier_d_achat" filePath="app\src\main\res\layout\fragment_magasin.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/fragment_magasin_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="124" endOffset="53"/></Target><Target id="@+id/layout_header" view="LinearLayout"><Expressions/><location startLine="13" startOffset="8" endLine="43" endOffset="22"/></Target><Target id="@+id/image_view_logo" view="ImageView"><Expressions/><location startLine="26" startOffset="12" endLine="32" endOffset="53"/></Target><Target id="@+id/text_view_titre_magasin" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="41" endOffset="42"/></Target><Target id="@+id/text_view_admin_indicator" view="TextView"><Expressions/><location startLine="46" startOffset="8" endLine="63" endOffset="70"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="65" startOffset="8" endLine="73" endOffset="55"/></Target><Target id="@+id/recycler_view_magasin" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="75" startOffset="8" endLine="96" endOffset="51"/></Target><Target id="@+id/text_view_liste_vide" view="TextView"><Expressions/><location startLine="98" startOffset="8" endLine="109" endOffset="55"/></Target><Target id="@+id/fab_ajouter_item" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="113" startOffset="4" endLine="122" endOffset="41"/></Target></Targets></Layout>