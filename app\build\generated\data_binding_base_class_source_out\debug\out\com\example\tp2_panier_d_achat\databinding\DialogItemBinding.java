// Generated by view binder compiler. Do not edit!
package com.example.tp2_panier_d_achat.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.Spinner;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.tp2_panier_d_achat.R;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogItemBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextInputEditText editTextDescription;

  @NonNull
  public final TextInputEditText editTextNom;

  @NonNull
  public final TextInputEditText editTextPrix;

  @NonNull
  public final Spinner spinnerCategorie;

  private DialogItemBinding(@NonNull LinearLayout rootView,
      @NonNull TextInputEditText editTextDescription, @NonNull TextInputEditText editTextNom,
      @NonNull TextInputEditText editTextPrix, @NonNull Spinner spinnerCategorie) {
    this.rootView = rootView;
    this.editTextDescription = editTextDescription;
    this.editTextNom = editTextNom;
    this.editTextPrix = editTextPrix;
    this.spinnerCategorie = spinnerCategorie;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.edit_text_description;
      TextInputEditText editTextDescription = ViewBindings.findChildViewById(rootView, id);
      if (editTextDescription == null) {
        break missingId;
      }

      id = R.id.edit_text_nom;
      TextInputEditText editTextNom = ViewBindings.findChildViewById(rootView, id);
      if (editTextNom == null) {
        break missingId;
      }

      id = R.id.edit_text_prix;
      TextInputEditText editTextPrix = ViewBindings.findChildViewById(rootView, id);
      if (editTextPrix == null) {
        break missingId;
      }

      id = R.id.spinner_categorie;
      Spinner spinnerCategorie = ViewBindings.findChildViewById(rootView, id);
      if (spinnerCategorie == null) {
        break missingId;
      }

      return new DialogItemBinding((LinearLayout) rootView, editTextDescription, editTextNom,
          editTextPrix, spinnerCategorie);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
