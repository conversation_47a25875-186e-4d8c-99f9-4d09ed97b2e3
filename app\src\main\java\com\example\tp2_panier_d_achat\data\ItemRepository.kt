package com.example.tp2_panier_d_achat.data

import androidx.lifecycle.LiveData

/**
 * Repository pour la gestion des items
 * Sert d'interface entre les ViewModels et la base de données
 * Suit le pattern Repository recommandé par Google
 */
class ItemRepository(private val itemDao: ItemDao) {
    
    /**
     * Récupère tous les items sous forme de LiveData
     * Permet l'observation automatique des changements
     */
    val allItems: LiveData<List<Item>> = itemDao.getAllItems()
    
    /**
     * Récupère un item par son ID
     */
    suspend fun getItemById(id: Long): Item? {
        return itemDao.getItemById(id)
    }
    
    /**
     * Récupère tous les items d'une catégorie
     */
    suspend fun getItemsByCategorie(categorie: String): List<Item> {
        return itemDao.getItemsByCategorie(categorie)
    }
    
    /**
     * Insère un nouvel item
     */
    suspend fun insertItem(item: Item): Long {
        return itemDao.insertItem(item)
    }
    
    /**
     * Met à jour un item existant
     */
    suspend fun updateItem(item: Item) {
        itemDao.updateItem(item)
    }
    
    /**
     * Supprime un item
     */
    suspend fun deleteItem(item: Item) {
        itemDao.deleteItem(item)
    }
    
    /**
     * Supprime tous les items
     */
    suspend fun deleteAllItems() {
        itemDao.deleteAllItems()
    }
    
    /**
     * Compte le nombre total d'items
     */
    suspend fun getItemCount(): Int {
        return itemDao.getItemCount()
    }
}
