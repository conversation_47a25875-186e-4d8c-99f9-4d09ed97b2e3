// Generated by view binder compiler. Do not edit!
package com.example.tp2_panier_d_achat.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.tp2_panier_d_achat.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemMagasinBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final CardView cardViewItem;

  @NonNull
  public final ImageView imageViewItem;

  @NonNull
  public final ImageView ivMenuIndicator;

  @NonNull
  public final TextView textViewCategorie;

  @NonNull
  public final TextView textViewDescription;

  @NonNull
  public final TextView textViewNom;

  @NonNull
  public final TextView textViewPrix;

  private ItemMagasinBinding(@NonNull CardView rootView, @NonNull CardView cardViewItem,
      @NonNull ImageView imageViewItem, @NonNull ImageView ivMenuIndicator,
      @NonNull TextView textViewCategorie, @NonNull TextView textViewDescription,
      @NonNull TextView textViewNom, @NonNull TextView textViewPrix) {
    this.rootView = rootView;
    this.cardViewItem = cardViewItem;
    this.imageViewItem = imageViewItem;
    this.ivMenuIndicator = ivMenuIndicator;
    this.textViewCategorie = textViewCategorie;
    this.textViewDescription = textViewDescription;
    this.textViewNom = textViewNom;
    this.textViewPrix = textViewPrix;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemMagasinBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemMagasinBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_magasin, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemMagasinBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      CardView cardViewItem = (CardView) rootView;

      id = R.id.image_view_item;
      ImageView imageViewItem = ViewBindings.findChildViewById(rootView, id);
      if (imageViewItem == null) {
        break missingId;
      }

      id = R.id.ivMenuIndicator;
      ImageView ivMenuIndicator = ViewBindings.findChildViewById(rootView, id);
      if (ivMenuIndicator == null) {
        break missingId;
      }

      id = R.id.text_view_categorie;
      TextView textViewCategorie = ViewBindings.findChildViewById(rootView, id);
      if (textViewCategorie == null) {
        break missingId;
      }

      id = R.id.text_view_description;
      TextView textViewDescription = ViewBindings.findChildViewById(rootView, id);
      if (textViewDescription == null) {
        break missingId;
      }

      id = R.id.text_view_nom;
      TextView textViewNom = ViewBindings.findChildViewById(rootView, id);
      if (textViewNom == null) {
        break missingId;
      }

      id = R.id.text_view_prix;
      TextView textViewPrix = ViewBindings.findChildViewById(rootView, id);
      if (textViewPrix == null) {
        break missingId;
      }

      return new ItemMagasinBinding((CardView) rootView, cardViewItem, imageViewItem,
          ivMenuIndicator, textViewCategorie, textViewDescription, textViewNom, textViewPrix);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
