<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_item" modulePackage="com.example.tp2_panier_d_achat" filePath="app\src\main\res\layout\dialog_item.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_item_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="69" endOffset="14"/></Target><Target id="@+id/edit_text_nom" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="14" startOffset="8" endLine="19" endOffset="34"/></Target><Target id="@+id/edit_text_description" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="30" startOffset="8" endLine="35" endOffset="34"/></Target><Target id="@+id/edit_text_prix" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="46" startOffset="8" endLine="51" endOffset="34"/></Target><Target id="@+id/spinner_categorie" view="Spinner"><Expressions/><location startLine="63" startOffset="4" endLine="67" endOffset="44"/></Target></Targets></Layout>