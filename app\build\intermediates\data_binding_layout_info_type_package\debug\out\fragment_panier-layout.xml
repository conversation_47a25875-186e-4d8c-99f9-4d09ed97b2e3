<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_panier" modulePackage="com.example.tp2_panier_d_achat" filePath="app\src\main\res\layout\fragment_panier.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_panier_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="170" endOffset="51"/></Target><Target id="@+id/text_view_titre_panier" view="TextView"><Expressions/><location startLine="8" startOffset="4" endLine="18" endOffset="51"/></Target><Target id="@+id/layout_panier_content" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="21" startOffset="4" endLine="138" endOffset="55"/></Target><Target id="@+id/text_view_nombre_items" view="TextView"><Expressions/><location startLine="33" startOffset="8" endLine="43" endOffset="37"/></Target><Target id="@+id/recycler_view_panier" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="46" startOffset="8" endLine="67" endOffset="50"/></Target><Target id="@+id/layout_total" view="LinearLayout"><Expressions/><location startLine="70" startOffset="8" endLine="136" endOffset="22"/></Target><Target id="@+id/text_view_sous_total" view="TextView"><Expressions/><location startLine="83" startOffset="12" endLine="92" endOffset="49"/></Target><Target id="@+id/text_view_taxe" view="TextView"><Expressions/><location startLine="95" startOffset="12" endLine="104" endOffset="48"/></Target><Target id="@+id/text_view_total" view="TextView"><Expressions/><location startLine="107" startOffset="12" endLine="117" endOffset="44"/></Target><Target id="@+id/button_valider_commande" view="Button"><Expressions/><location startLine="119" startOffset="12" endLine="126" endOffset="58"/></Target><Target id="@+id/button_vider_panier" view="Button"><Expressions/><location startLine="128" startOffset="12" endLine="134" endOffset="58"/></Target><Target id="@+id/layout_panier_vide" view="LinearLayout"><Expressions/><location startLine="141" startOffset="4" endLine="168" endOffset="18"/></Target></Targets></Layout>