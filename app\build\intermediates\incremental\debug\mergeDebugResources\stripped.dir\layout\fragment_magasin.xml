<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.magasin.MagasinFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- Logo et titre du magasin -->
        <LinearLayout
            android:id="@+id/layout_header"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/image_view_logo"
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:layout_marginEnd="8dp"
                android:contentDescription="@string/app_name"
                android:src="@drawable/ic_app_logo" />

            <TextView
                android:id="@+id/text_view_titre_magasin"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/titre_principal"
                android:textAlignment="center"
                android:textSize="20sp"
                android:textStyle="bold" />

        </LinearLayout>

        <!-- Indicateur mode admin -->
        <TextView
            android:id="@+id/text_view_admin_indicator"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="16dp"
            android:background="@android:color/holo_orange_dark"
            android:gravity="center"
            android:paddingVertical="8dp"
            android:text="🔧 MODE ADMINISTRATEUR ACTIVÉ - Clic long pour modifier/supprimer"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:textStyle="bold"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/layout_header" />

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view_magasin"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="8dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="8dp"
            android:layout_marginBottom="8dp"
            android:clipToPadding="false"
            android:paddingBottom="80dp"
            android:scrollbars="vertical"
            android:scrollbarStyle="outsideOverlay"
            android:scrollbarThumbVertical="@drawable/scrollbar_thumb"
            android:scrollbarTrackVertical="@drawable/scrollbar_track"
            android:fadeScrollbars="false"
            android:overScrollMode="ifContentScrolls"
            android:nestedScrollingEnabled="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/text_view_admin_indicator"
            tools:listitem="@layout/item_magasin" />

        <TextView
            android:id="@+id/text_view_liste_vide"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/msg_liste_vide"
            android:textSize="16sp"
            android:textColor="?android:attr/textColorSecondary"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_ajouter_item"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:contentDescription="@string/ajouter_item"
        android:src="@drawable/ic_add"
        android:visibility="gone"
        app:tint="@android:color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>