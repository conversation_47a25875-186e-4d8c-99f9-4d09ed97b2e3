// Generated by view binder compiler. Do not edit!
package com.example.tp2_panier_d_achat.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.tp2_panier_d_achat.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemPanierBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final ImageButton buttonAugmenter;

  @NonNull
  public final ImageButton buttonDiminuer;

  @NonNull
  public final ImageButton buttonSupprimer;

  @NonNull
  public final ImageView imageViewItemPanier;

  @NonNull
  public final LinearLayout layoutQuantite;

  @NonNull
  public final TextView textViewDescriptionPanier;

  @NonNull
  public final TextView textViewNomPanier;

  @NonNull
  public final TextView textViewPrixTotal;

  @NonNull
  public final TextView textViewPrixUnitaire;

  @NonNull
  public final TextView textViewQuantite;

  private ItemPanierBinding(@NonNull CardView rootView, @NonNull ImageButton buttonAugmenter,
      @NonNull ImageButton buttonDiminuer, @NonNull ImageButton buttonSupprimer,
      @NonNull ImageView imageViewItemPanier, @NonNull LinearLayout layoutQuantite,
      @NonNull TextView textViewDescriptionPanier, @NonNull TextView textViewNomPanier,
      @NonNull TextView textViewPrixTotal, @NonNull TextView textViewPrixUnitaire,
      @NonNull TextView textViewQuantite) {
    this.rootView = rootView;
    this.buttonAugmenter = buttonAugmenter;
    this.buttonDiminuer = buttonDiminuer;
    this.buttonSupprimer = buttonSupprimer;
    this.imageViewItemPanier = imageViewItemPanier;
    this.layoutQuantite = layoutQuantite;
    this.textViewDescriptionPanier = textViewDescriptionPanier;
    this.textViewNomPanier = textViewNomPanier;
    this.textViewPrixTotal = textViewPrixTotal;
    this.textViewPrixUnitaire = textViewPrixUnitaire;
    this.textViewQuantite = textViewQuantite;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemPanierBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemPanierBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_panier, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemPanierBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_augmenter;
      ImageButton buttonAugmenter = ViewBindings.findChildViewById(rootView, id);
      if (buttonAugmenter == null) {
        break missingId;
      }

      id = R.id.button_diminuer;
      ImageButton buttonDiminuer = ViewBindings.findChildViewById(rootView, id);
      if (buttonDiminuer == null) {
        break missingId;
      }

      id = R.id.button_supprimer;
      ImageButton buttonSupprimer = ViewBindings.findChildViewById(rootView, id);
      if (buttonSupprimer == null) {
        break missingId;
      }

      id = R.id.image_view_item_panier;
      ImageView imageViewItemPanier = ViewBindings.findChildViewById(rootView, id);
      if (imageViewItemPanier == null) {
        break missingId;
      }

      id = R.id.layout_quantite;
      LinearLayout layoutQuantite = ViewBindings.findChildViewById(rootView, id);
      if (layoutQuantite == null) {
        break missingId;
      }

      id = R.id.text_view_description_panier;
      TextView textViewDescriptionPanier = ViewBindings.findChildViewById(rootView, id);
      if (textViewDescriptionPanier == null) {
        break missingId;
      }

      id = R.id.text_view_nom_panier;
      TextView textViewNomPanier = ViewBindings.findChildViewById(rootView, id);
      if (textViewNomPanier == null) {
        break missingId;
      }

      id = R.id.text_view_prix_total;
      TextView textViewPrixTotal = ViewBindings.findChildViewById(rootView, id);
      if (textViewPrixTotal == null) {
        break missingId;
      }

      id = R.id.text_view_prix_unitaire;
      TextView textViewPrixUnitaire = ViewBindings.findChildViewById(rootView, id);
      if (textViewPrixUnitaire == null) {
        break missingId;
      }

      id = R.id.text_view_quantite;
      TextView textViewQuantite = ViewBindings.findChildViewById(rootView, id);
      if (textViewQuantite == null) {
        break missingId;
      }

      return new ItemPanierBinding((CardView) rootView, buttonAugmenter, buttonDiminuer,
          buttonSupprimer, imageViewItemPanier, layoutQuantite, textViewDescriptionPanier,
          textViewNomPanier, textViewPrixTotal, textViewPrixUnitaire, textViewQuantite);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
