<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="48dp"
    android:height="48dp"
    android:viewportWidth="48"
    android:viewportHeight="48">
    
    <!-- Background surface -->
    <path
        android:fillColor="#F5F5F5"
        android:pathData="M2,42 L46,42 L46,44 L2,44 Z" />
    
    <!-- Orange can (Oasis style) -->
    <path
        android:fillColor="#FF9800"
        android:strokeColor="#F57C00"
        android:strokeWidth="0.5"
        android:pathData="M4,38 L10,38 L10,12 L4,12 Z" />
    
    <!-- Orange can top -->
    <path
        android:fillColor="#FFB74D"
        android:pathData="M4,12 L10,12 L10,10 L4,10 Z" />
    
    <!-- Orange can label -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M4.5,30 L9.5,30 L9.5,20 L4.5,20 Z" />
    
    <!-- <PERSON> can text simulation -->
    <path
        android:fillColor="#FF5722"
        android:pathData="M5,28 L9,28 L9,27 L5,27 Z" />
    <path
        android:fillColor="#FF5722"
        android:pathData="M5,25 L9,25 L9,24 L5,24 Z" />
    
    <!-- Red can (Coca-Cola style) -->
    <path
        android:fillColor="#D32F2F"
        android:strokeColor="#B71C1C"
        android:strokeWidth="0.5"
        android:pathData="M12,38 L18,38 L18,12 L12,12 Z" />
    
    <!-- Red can top -->
    <path
        android:fillColor="#F44336"
        android:pathData="M12,12 L18,12 L18,10 L12,10 Z" />
    
    <!-- Red can logo area -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M12.5,30 L17.5,30 L17.5,20 L12.5,20 Z" />
    
    <!-- Red can curved text simulation -->
    <path
        android:fillColor="#D32F2F"
        android:pathData="M13,28 Q15,26 17,28 L17,27 Q15,25 13,27 Z" />
    <path
        android:fillColor="#D32F2F"
        android:pathData="M13,25 L17,25 L17,24 L13,24 Z" />
    
    <!-- Purple can (Oasis Cassis style) -->
    <path
        android:fillColor="#8E24AA"
        android:strokeColor="#6A1B9A"
        android:strokeWidth="0.5"
        android:pathData="M20,38 L26,38 L26,12 L20,12 Z" />
    
    <!-- Purple can top -->
    <path
        android:fillColor="#AB47BC"
        android:pathData="M20,12 L26,12 L26,10 L20,10 Z" />
    
    <!-- Purple can dual label -->
    <path
        android:fillColor="#E1BEE7"
        android:pathData="M20.5,32 L25.5,32 L25.5,28 L20.5,28 Z" />
    <path
        android:fillColor="#2196F3"
        android:pathData="M20.5,26 L25.5,26 L25.5,18 L20.5,18 Z" />
    
    <!-- Purple can text -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M21,30 L25,30 L25,29 L21,29 Z" />
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M21,24 L25,24 L25,23 L21,23 Z" />
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M21,21 L25,21 L25,20 L21,20 Z" />
    
    <!-- Green can (Perrier style) -->
    <path
        android:fillColor="#2E7D32"
        android:strokeColor="#1B5E20"
        android:strokeWidth="0.5"
        android:pathData="M28,38 L34,38 L34,12 L28,12 Z" />
    
    <!-- Green can top -->
    <path
        android:fillColor="#4CAF50"
        android:pathData="M28,12 L34,12 L34,10 L28,10 Z" />
    
    <!-- Green can label -->
    <path
        android:fillColor="#C8E6C9"
        android:pathData="M28.5,30 L33.5,30 L33.5,18 L28.5,18 Z" />
    
    <!-- Green can logo simulation -->
    <path
        android:fillColor="#2E7D32"
        android:pathData="M29,28 Q31,26 33,28 L33,27 Q31,25 29,27 Z" />
    <path
        android:fillColor="#2E7D32"
        android:pathData="M29,25 L33,25 L33,24 L29,24 Z" />
    <path
        android:fillColor="#2E7D32"
        android:pathData="M29,22 L33,22 L33,21 L29,21 Z" />
    
    <!-- Blue can (additional variety) -->
    <path
        android:fillColor="#1976D2"
        android:strokeColor="#0D47A1"
        android:strokeWidth="0.5"
        android:pathData="M36,38 L42,38 L42,12 L36,12 Z" />
    
    <!-- Blue can top -->
    <path
        android:fillColor="#2196F3"
        android:pathData="M36,12 L42,12 L42,10 L36,10 Z" />
    
    <!-- Blue can label -->
    <path
        android:fillColor="#E3F2FD"
        android:pathData="M36.5,30 L41.5,30 L41.5,20 L36.5,20 Z" />
    
    <!-- Blue can text -->
    <path
        android:fillColor="#1976D2"
        android:pathData="M37,28 L41,28 L41,27 L37,27 Z" />
    <path
        android:fillColor="#1976D2"
        android:pathData="M37,25 L41,25 L41,24 L37,24 Z" />
    <path
        android:fillColor="#1976D2"
        android:pathData="M37,22 L41,22 L41,21 L37,21 Z" />
    
    <!-- Bubbles/carbonation effect -->
    <path
        android:fillColor="#E3F2FD"
        android:fillAlpha="0.7"
        android:pathData="M6,16 Q6,15 7,15 Q8,15 8,16 Q8,17 7,17 Q6,17 6,16 Z" />
    <path
        android:fillColor="#FFEBEE"
        android:fillAlpha="0.7"
        android:pathData="M14,18 Q14,17 15,17 Q16,17 16,18 Q16,19 15,19 Q14,19 14,18 Z" />
    <path
        android:fillColor="#F3E5F5"
        android:fillAlpha="0.7"
        android:pathData="M22,16 Q22,15 23,15 Q24,15 24,16 Q24,17 23,17 Q22,17 22,16 Z" />
    <path
        android:fillColor="#E8F5E8"
        android:fillAlpha="0.7"
        android:pathData="M30,17 Q30,16 31,16 Q32,16 32,17 Q32,18 31,18 Q30,18 30,17 Z" />
    <path
        android:fillColor="#E3F2FD"
        android:fillAlpha="0.7"
        android:pathData="M38,15 Q38,14 39,14 Q40,14 40,15 Q40,16 39,16 Q38,16 38,15 Z" />
    
</vector>
