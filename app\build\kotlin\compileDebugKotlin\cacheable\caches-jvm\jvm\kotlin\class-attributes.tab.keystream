+com.example.tp2_panier_d_achat.MainActivity/com.example.tp2_panier_d_achat.data.AppDatabase9com.example.tp2_panier_d_achat.data.AppDatabase.CompanionJcom.example.tp2_panier_d_achat.data.AppDatabase.Companion.DatabaseCallback(com.example.tp2_panier_d_achat.data.Item+com.example.tp2_panier_d_achat.data.ItemDao2com.example.tp2_panier_d_achat.data.ItemRepository.com.example.tp2_panier_d_achat.data.PanierItem9com.example.tp2_panier_d_achat.ui.adapters.MagasinAdapterHcom.example.tp2_panier_d_achat.ui.adapters.MagasinAdapter.ItemViewHolderJcom.example.tp2_panier_d_achat.ui.adapters.MagasinAdapter.ItemDiffCallback8com.example.tp2_panier_d_achat.ui.adapters.PanierAdapterIcom.example.tp2_panier_d_achat.ui.adapters.PanierAdapter.PanierViewHolderOcom.example.tp2_panier_d_achat.ui.adapters.PanierAdapter.PanierItemDiffCallback;com.example.tp2_panier_d_achat.ui.adapters.ViewPagerAdapter<<EMAIL>.tp2_panier_d_achat.ui.dialogs.QuantiteDialogFragmentJcom.example.tp2_panier_d_achat.ui.dialogs.QuantiteDialogFragment.Companion9com.example.tp2_panier_d_achat.ui.magasin.MagasinFragment7com.example.tp2_panier_d_achat.ui.panier.PanierFragment1com.example.tp2_panier_d_achat.utils.AdminManager;com.example.tp2_panier_d_achat.utils.AdminManager.Companion=com.example.tp2_panier_d_achat.utils.AdminManager.AdminAction<com.example.tp2_panier_d_achat.utils.AdminManager.AdminStats7com.example.tp2_panier_d_achat.utils.CategoryIconHelper7com.example.tp2_panier_d_achat.utils.PreferencesManagerAcom.example.tp2_panier_d_achat.utils.PreferencesManager.Companion2com.example.tp2_panier_d_achat.utils.TaxCalculatorAcom.example.tp2_panier_d_achat.utils.TaxCalculator.TaxCalculation4com.example.tp2_panier_d_achat.utils.ValidationUtilsEcom.example.tp2_panier_d_achat.utils.ValidationUtils.ValidationResult9com.example.tp2_panier_d_achat.viewmodel.MagasinViewModel8com.example.tp2_panier_d_achat.viewmodel.PanierViewModel=com.example.tp2_panier_d_achat.databinding.ItemMagasinBinding<com.example.tp2_panier_d_achat.databinding.DialogItemBinding<<EMAIL>.tp2_panier_d_achat.databinding.FragmentPanierBinding>com.example.tp2_panier_d_achat.databinding.ActivityMainBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 