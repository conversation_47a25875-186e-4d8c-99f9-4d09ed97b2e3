<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_panier" modulePackage="com.example.tp2_panier_d_achat" filePath="app\src\main\res\layout\item_panier.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_panier_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="145" endOffset="35"/></Target><Target id="@+id/image_view_item_panier" view="ImageView"><Expressions/><location startLine="16" startOffset="8" endLine="25" endOffset="55"/></Target><Target id="@+id/text_view_nom_panier" view="TextView"><Expressions/><location startLine="28" startOffset="8" endLine="40" endOffset="43"/></Target><Target id="@+id/text_view_description_panier" view="TextView"><Expressions/><location startLine="43" startOffset="8" endLine="56" endOffset="51"/></Target><Target id="@+id/text_view_prix_unitaire" view="TextView"><Expressions/><location startLine="59" startOffset="8" endLine="70" endOffset="41"/></Target><Target id="@+id/layout_quantite" view="LinearLayout"><Expressions/><location startLine="73" startOffset="8" endLine="112" endOffset="22"/></Target><Target id="@+id/button_diminuer" view="ImageButton"><Expressions/><location startLine="83" startOffset="12" endLine="90" endOffset="47"/></Target><Target id="@+id/text_view_quantite" view="TextView"><Expressions/><location startLine="93" startOffset="12" endLine="100" endOffset="32"/></Target><Target id="@+id/button_augmenter" view="ImageButton"><Expressions/><location startLine="103" startOffset="12" endLine="110" endOffset="47"/></Target><Target id="@+id/text_view_prix_total" view="TextView"><Expressions/><location startLine="115" startOffset="8" endLine="128" endOffset="33"/></Target><Target id="@+id/button_supprimer" view="ImageButton"><Expressions/><location startLine="131" startOffset="8" endLine="141" endOffset="41"/></Target></Targets></Layout>