package com.example.tp2_panier_d_achat.utils

/**
 * Utilitaires de validation pour l'application
 */
object ValidationUtils {
    
    /**
     * Valide le nom d'un item
     */
    fun isValidItemName(name: String): Boolean {
        return name.trim().isNotEmpty() && name.trim().length >= 2
    }
    
    /**
     * Valide la description d'un item
     */
    fun isValidItemDescription(description: String): Boolean {
        return description.trim().isNotEmpty() && description.trim().length >= 5
    }
    
    /**
     * Valide le prix d'un item
     */
    fun isValidPrice(priceText: String): Pair<Boolean, Double?> {
        return try {
            val price = priceText.trim().toDouble()
            if (price > 0 && price <= 10000) { // Prix maximum de 10,000$
                Pair(true, price)
            } else {
                Pair(false, null)
            }
        } catch (e: NumberFormatException) {
            Pair(false, null)
        }
    }
    
    /**
     * Valide une quantité
     */
    fun isValidQuantity(quantity: Int): Boolean {
        return quantity in 1..100 // Quantité entre 1 et 100
    }
    
    /**
     * Valide une catégorie
     */
    fun isValidCategory(category: String): Boolean {
        val validCategories = listOf("fruits", "legumes", "laitier", "viande", "boulangerie", "autre")
        return category.lowercase() in validCategories
    }
    
    /**
     * Nettoie et formate un nom d'item
     */
    fun formatItemName(name: String): String {
        return name.trim().replaceFirstChar { 
            if (it.isLowerCase()) it.titlecase() else it.toString() 
        }
    }
    
    /**
     * Nettoie et formate une description
     */
    fun formatDescription(description: String): String {
        return description.trim().replaceFirstChar { 
            if (it.isLowerCase()) it.titlecase() else it.toString() 
        }
    }
    
    /**
     * Formate un prix pour l'affichage
     */
    fun formatPrice(price: Double): String {
        return String.format("%.2f$", price)
    }
    
    /**
     * Valide tous les champs d'un item
     */
    fun validateItemFields(
        name: String,
        description: String,
        priceText: String,
        category: String
    ): ValidationResult {
        val errors = mutableListOf<String>()
        
        if (!isValidItemName(name)) {
            errors.add("Le nom doit contenir au moins 2 caractères")
        }
        
        if (!isValidItemDescription(description)) {
            errors.add("La description doit contenir au moins 5 caractères")
        }
        
        val (isPriceValid, price) = isValidPrice(priceText)
        if (!isPriceValid) {
            errors.add("Le prix doit être un nombre positif inférieur à 10,000$")
        }
        
        if (!isValidCategory(category)) {
            errors.add("Catégorie invalide")
        }
        
        return ValidationResult(
            isValid = errors.isEmpty(),
            errors = errors,
            validatedPrice = price
        )
    }
    
    /**
     * Classe pour le résultat de validation
     */
    data class ValidationResult(
        val isValid: Boolean,
        val errors: List<String>,
        val validatedPrice: Double? = null
    )
}
