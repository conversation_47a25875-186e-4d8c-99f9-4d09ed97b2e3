package com.example.tp2_panier_d_achat.data

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.io.Serializable

/**
 * Entité représentant un item dans le magasin
 * Cette classe définit la structure de données pour les articles vendus
 */
@Entity(tableName = "items")
data class Item(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    /**
     * Nom de l'item (ex: "Pommes", "Lait", etc.)
     */
    val nom: String,
    
    /**
     * Description détaillée de l'item
     */
    val description: String,
    
    /**
     * Prix unitaire de l'item en dollars
     */
    val prix: Double,
    
    /**
     * Catégorie de l'item (utilisée pour choisir l'image)
     * Valeurs possibles: "fruits", "legumes", "laitier", "viande", "boulangerie", "autre"
     */
    val categorie: String,
    
    /**
     * Quantité par défaut (toujours 1 pour les items en magasin)
     */
    val quantite: Int = 1
) : Serializable {
    /**
     * Retourne le nom de l'image associée à la catégorie
     */
    fun getImageResource(): String {
        return when (categorie.lowercase()) {
            "fruits" -> "ic_fruits"
            "legumes" -> "ic_legumes"
            "laitier" -> "ic_laitier"
            "viande" -> "ic_viande"
            "boulangerie" -> "ic_boulangerie"
            else -> "ic_defaut_item"
        }
    }
}
