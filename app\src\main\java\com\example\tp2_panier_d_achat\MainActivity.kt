package com.example.tp2_panier_d_achat

import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import com.google.android.material.appbar.MaterialToolbar
import com.example.tp2_panier_d_achat.databinding.ActivityMainBinding
import com.example.tp2_panier_d_achat.ui.adapters.ViewPagerAdapter
import com.example.tp2_panier_d_achat.utils.AdminManager
import com.example.tp2_panier_d_achat.viewmodel.MagasinViewModel
import com.example.tp2_panier_d_achat.viewmodel.PanierViewModel
import com.google.android.material.bottomnavigation.BottomNavigationView

/**
 * Activité principale de l'application
 * Gère la navigation entre les fragments et le menu administrateur
 */
class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    private lateinit var toolbar: MaterialToolbar

    // ViewModels partagés entre les fragments
    private lateinit var magasinViewModel: MagasinViewModel
    private lateinit var panierViewModel: PanierViewModel
    private lateinit var adminManager: AdminManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialiser les ViewModels et AdminManager
        magasinViewModel = ViewModelProvider(this)[MagasinViewModel::class.java]
        panierViewModel = ViewModelProvider(this)[PanierViewModel::class.java]
        adminManager = AdminManager.getInstance(this)

        setupToolbar()
        setupNavigation()
        observeViewModel()
    }

    /**
     * Configure la Toolbar personnalisée
     */
    private fun setupToolbar() {
        toolbar = findViewById(R.id.toolbar)
        setSupportActionBar(toolbar)

        // Configuration du titre et du logo
        supportActionBar?.apply {
            setDisplayShowTitleEnabled(false) // Désactiver le titre par défaut
            setDisplayShowHomeEnabled(false)
        }
    }

    /**
     * Configure la navigation entre les fragments
     */
    private fun setupNavigation() {
        val navView: BottomNavigationView = binding.navView

        // Utiliser ViewPager2 pour tous les modes (portrait et paysage)
        setupPortraitLayout(navView)
    }

    /**
     * Configure le layout en mode portrait avec ViewPager2
     */
    private fun setupPortraitLayout(navView: BottomNavigationView) {
        val viewPager = binding.viewPager

        // Configuration du ViewPager2
            // Configuration du ViewPager2 avec l'adapter
            val adapter = ViewPagerAdapter(this)
            viewPager.adapter = adapter

            // Synchronisation entre BottomNavigationView et ViewPager2
            navView.setOnItemSelectedListener { item ->
                when (item.itemId) {
                    R.id.navigation_magasin -> {
                        viewPager.currentItem = 0
                        true
                    }
                    R.id.navigation_panier -> {
                        viewPager.currentItem = 1
                        true
                    }
                    else -> false
                }
            }

            // Synchronisation inverse : ViewPager2 vers BottomNavigationView
            viewPager.registerOnPageChangeCallback(object : androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    when (position) {
                        0 -> navView.selectedItemId = R.id.navigation_magasin
                        1 -> navView.selectedItemId = R.id.navigation_panier
                    }
                }
            })
    }





    /**
     * Observe les changements dans les ViewModels
     */
    private fun observeViewModel() {
        // Observer les messages d'erreur du magasin
        magasinViewModel.errorMessage.observe(this) { message ->
            message?.let {
                Toast.makeText(this, it, Toast.LENGTH_LONG).show()
                magasinViewModel.clearErrorMessage()
            }
        }

        // Observer le mode administrateur
        magasinViewModel.isAdminMode.observe(this) { isAdmin ->
            val message = if (isAdmin) {
                getString(R.string.mode_admin_active)
            } else {
                getString(R.string.mode_admin_desactive)
            }
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show()

            // Mettre à jour l'indicateur admin dans la toolbar
            updateToolbarAdminIndicator(isAdmin)

            // Mettre à jour le menu pour afficher/masquer le bouton de déconnexion
            invalidateOptionsMenu()
        }
    }

    /**
     * Met à jour l'indicateur admin dans la toolbar
     */
    private fun updateToolbarAdminIndicator(isAdmin: Boolean) {
        val adminIndicator = findViewById<android.widget.TextView>(R.id.toolbar_admin_indicator)
        adminIndicator?.visibility = if (isAdmin) View.VISIBLE else View.GONE
    }

    /**
     * Crée le menu d'options dans la barre d'action
     */
    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.main_menu, menu)
        return true
    }

    /**
     * Prépare le menu avant affichage (contrôle la visibilité des éléments)
     */
    override fun onPrepareOptionsMenu(menu: Menu?): Boolean {
        menu?.let {
            val isAdminMode = magasinViewModel.isAdminMode.value ?: false

            // Afficher le bouton "Admin" seulement si pas en mode admin
            it.findItem(R.id.action_admin)?.isVisible = !isAdminMode

            // Afficher le bouton "Quitter mode admin" seulement si en mode admin
            it.findItem(R.id.action_logout_admin)?.isVisible = isAdminMode
        }
        return super.onPrepareOptionsMenu(menu)
    }



    /**
     * Gère les clics sur les éléments du menu
     */
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_admin -> {
                // Basculer le mode administrateur
                magasinViewModel.toggleAdminMode()
                true
            }
            R.id.action_logout_admin -> {
                // Quitter le mode administrateur
                magasinViewModel.setAdminMode(false)
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    /**
     * Retourne le ViewModel du magasin pour les fragments
     */
    fun getMagasinViewModel(): MagasinViewModel = magasinViewModel

    /**
     * Retourne le ViewModel du panier pour les fragments
     */
    fun getPanierViewModel(): PanierViewModel = panierViewModel
}