package com.example.tp2_panier_d_achat.ui.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.tp2_panier_d_achat.R
import com.example.tp2_panier_d_achat.data.Item
import com.example.tp2_panier_d_achat.databinding.ItemMagasinBinding
import com.example.tp2_panier_d_achat.utils.CategoryIconHelper

/**
 * Adapter pour le RecyclerView du fragment Magasin
 * Gère l'affichage des items et les interactions utilisateur
 */
class MagasinAdapter(
    private val onItemClick: (Item) -> Unit,
    private val onItemLongClick: (Item) -> Unit,
    private val isAdminMode: () -> Boolean
) : ListAdapter<Item, MagasinAdapter.ItemViewHolder>(ItemDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ItemViewHolder {
        val binding = ItemMagasinBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ItemViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ItemViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class ItemViewHolder(
        private val binding: ItemMagasinBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(item: Item) {
            binding.apply {
                // Afficher les informations de l'item
                textViewNom.text = item.nom
                textViewDescription.text = item.description
                textViewPrix.text = String.format("%.2f$", item.prix)
                textViewCategorie.text = item.categorie

                // Définir l'image selon la catégorie
                val imageResource = CategoryIconHelper.getIconResource(item.categorie)
                imageViewItem.setImageResource(imageResource)
                imageViewItem.contentDescription = CategoryIconHelper.getIconDescription(item.categorie)

                // Gestion des clics
                root.setOnClickListener {
                    onItemClick(item)
                }

                // Gestion du clic long (seulement en mode admin)
                root.setOnLongClickListener {
                    if (isAdminMode()) {
                        onItemLongClick(item)
                        true
                    } else {
                        false
                    }
                }

                // Indicateur visuel du mode admin
                if (isAdminMode()) {
                    root.alpha = 0.9f
                    // Indication visuelle pour le mode admin avec couleur de fond
                    cardViewItem.setCardBackgroundColor(
                        ContextCompat.getColor(root.context, android.R.color.holo_blue_light)
                    )
                    // Afficher l'indicateur de menu contextuel
                    ivMenuIndicator.visibility = android.view.View.VISIBLE
                } else {
                    root.alpha = 1.0f
                    // Couleur normale
                    cardViewItem.setCardBackgroundColor(
                        ContextCompat.getColor(root.context, android.R.color.white)
                    )
                    // Masquer l'indicateur de menu contextuel
                    ivMenuIndicator.visibility = android.view.View.GONE
                }
            }
        }


    }

    /**
     * DiffCallback pour optimiser les mises à jour de la liste
     */
    class ItemDiffCallback : DiffUtil.ItemCallback<Item>() {
        override fun areItemsTheSame(oldItem: Item, newItem: Item): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Item, newItem: Item): Boolean {
            return oldItem == newItem
        }
    }
}
