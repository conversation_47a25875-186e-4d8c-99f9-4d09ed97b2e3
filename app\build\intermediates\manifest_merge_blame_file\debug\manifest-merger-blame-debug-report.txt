1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.tp2_panier_d_achat"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
8-->C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="33" />
9-->C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml
10
11    <permission
11-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9850f61f88e3ad6016154401f7e52e36\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.example.tp2_panier_d_achat.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9850f61f88e3ad6016154401f7e52e36\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9850f61f88e3ad6016154401f7e52e36\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.example.tp2_panier_d_achat.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9850f61f88e3ad6016154401f7e52e36\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9850f61f88e3ad6016154401f7e52e36\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:5:5-25:19
18        android:allowBackup="true"
18-->C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\9850f61f88e3ad6016154401f7e52e36\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:7:9-65
21        android:debuggable="true"
22        android:extractNativeLibs="false"
23        android:fullBackupContent="@xml/backup_rules"
23-->C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:8:9-54
24        android:icon="@mipmap/ic_launcher"
24-->C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:9:9-43
25        android:label="@string/app_name"
25-->C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:10:9-41
26        android:roundIcon="@mipmap/ic_launcher_round"
26-->C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:11:9-54
27        android:supportsRtl="true"
27-->C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:12:9-35
28        android:theme="@style/Theme.TP2_Panier_D_Achat" >
28-->C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:13:9-56
29        <activity
29-->C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:14:9-24:20
30            android:name="com.example.tp2_panier_d_achat.MainActivity"
30-->C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:15:13-41
31            android:configChanges="orientation|screenSize|keyboardHidden"
31-->C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:18:13-74
32            android:exported="true"
32-->C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:16:13-36
33            android:label="@string/app_name" >
33-->C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:17:13-45
34            <intent-filter>
34-->C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:19:13-23:29
35                <action android:name="android.intent.action.MAIN" />
35-->C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:20:17-69
35-->C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:20:25-66
36
37                <category android:name="android.intent.category.LAUNCHER" />
37-->C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:22:17-77
37-->C:\Users\<USER>\AndroidStudioProjects\TP2_Panier_D_Achat\app\src\main\AndroidManifest.xml:22:27-74
38            </intent-filter>
39        </activity>
40
41        <provider
41-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\729c6a4d2da094711eb7f2ef40fd6c8f\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
42            android:name="androidx.startup.InitializationProvider"
42-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\729c6a4d2da094711eb7f2ef40fd6c8f\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
43            android:authorities="com.example.tp2_panier_d_achat.androidx-startup"
43-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\729c6a4d2da094711eb7f2ef40fd6c8f\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
44            android:exported="false" >
44-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\729c6a4d2da094711eb7f2ef40fd6c8f\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
45            <meta-data
45-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\729c6a4d2da094711eb7f2ef40fd6c8f\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
46                android:name="androidx.emoji2.text.EmojiCompatInitializer"
46-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\729c6a4d2da094711eb7f2ef40fd6c8f\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
47                android:value="androidx.startup" />
47-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\729c6a4d2da094711eb7f2ef40fd6c8f\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
48            <meta-data
48-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\8e69fe915bd06a2a15982c2936174ade\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
49                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
49-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\8e69fe915bd06a2a15982c2936174ade\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
50                android:value="androidx.startup" />
50-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\8e69fe915bd06a2a15982c2936174ade\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
51            <meta-data
51-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
52                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
52-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
53                android:value="androidx.startup" />
53-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
54        </provider>
55
56        <uses-library
56-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\256f6a6b293e0af0350a35948f95cbd9\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
57            android:name="androidx.window.extensions"
57-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\256f6a6b293e0af0350a35948f95cbd9\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
58            android:required="false" />
58-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\256f6a6b293e0af0350a35948f95cbd9\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
59        <uses-library
59-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\256f6a6b293e0af0350a35948f95cbd9\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
60            android:name="androidx.window.sidecar"
60-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\256f6a6b293e0af0350a35948f95cbd9\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
61            android:required="false" />
61-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\256f6a6b293e0af0350a35948f95cbd9\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
62
63        <service
63-->[androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c6eb3a7f9a5aea60146e759f475b8658\transformed\room-runtime-2.4.3\AndroidManifest.xml:25:9-28:40
64            android:name="androidx.room.MultiInstanceInvalidationService"
64-->[androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c6eb3a7f9a5aea60146e759f475b8658\transformed\room-runtime-2.4.3\AndroidManifest.xml:26:13-74
65            android:directBootAware="true"
65-->[androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c6eb3a7f9a5aea60146e759f475b8658\transformed\room-runtime-2.4.3\AndroidManifest.xml:27:13-43
66            android:exported="false" />
66-->[androidx.room:room-runtime:2.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c6eb3a7f9a5aea60146e759f475b8658\transformed\room-runtime-2.4.3\AndroidManifest.xml:28:13-37
67
68        <receiver
68-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
69            android:name="androidx.profileinstaller.ProfileInstallReceiver"
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
70            android:directBootAware="false"
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
71            android:enabled="true"
71-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
72            android:exported="true"
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
73            android:permission="android.permission.DUMP" >
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
74            <intent-filter>
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
75                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
76            </intent-filter>
77            <intent-filter>
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
78                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
79            </intent-filter>
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
81                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
82            </intent-filter>
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
84                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e115f8d4800edb210ff176ed524d2b4\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
85            </intent-filter>
86        </receiver>
87    </application>
88
89</manifest>
