@echo off
echo Creating ultra-light Android emulator...

set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk

echo.
echo Available system images:
"%ANDROID_HOME%\cmdline-tools\latest\bin\sdkmanager.bat" --list | findstr "system-images"

echo.
echo Creating AVD with minimal configuration...
echo no | "%ANDROID_HOME%\cmdline-tools\latest\bin\avdmanager.bat" create avd ^
    -n LightEmulator ^
    -k "system-images;android-28;google_apis;x86_64" ^
    -d "Nexus 5X" ^
    --force

echo.
echo Configuring minimal storage...
echo disk.dataPartition.size=1536M >> "%USERPROFILE%\.android\avd\LightEmulator.avd\config.ini"
echo hw.ramSize=1024 >> "%USERPROFILE%\.android\avd\LightEmulator.avd\config.ini"
echo sdcard.size=128M >> "%USERPROFILE%\.android\avd\LightEmulator.avd\config.ini"

echo.
echo Light emulator created! Starting...
"%ANDROID_HOME%\emulator\emulator.exe" -avd LightEmulator -memory 1024 -partition-size 1536
