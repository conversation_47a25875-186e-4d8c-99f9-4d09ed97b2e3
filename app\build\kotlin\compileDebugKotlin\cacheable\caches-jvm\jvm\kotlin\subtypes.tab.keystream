(androidx.recyclerview.widget.ListAdapter4androidx.recyclerview.widget.RecyclerView.ViewHolder2androidx.recyclerview.widget.DiffUtil.ItemCallback$androidx.fragment.app.DialogFragmentandroidx.fragment.app.Fragment androidx.viewbinding.ViewBinding(androidx.appcompat.app.AppCompatActivityandroidx.room.RoomDatabase#androidx.room.RoomDatabase.Callbackjava.io.Serializable0androidx.viewpager2.adapter.FragmentStateAdapterkotlin.Enum#androidx.lifecycle.AndroidViewModelandroidx.lifecycle.ViewModel                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               