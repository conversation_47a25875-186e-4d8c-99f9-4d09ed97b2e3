{"logs": [{"outputFile": "com.example.tp2_panier_d_achat.app-mergeDebugResources-51:/values-bg/values-bg.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\603bf3700fd8597ee55f4295d905ced2\\transformed\\navigation-ui-2.5.3\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,123", "endOffsets": "163,287"}, "to": {"startLines": "94,95", "startColumns": "4,4", "startOffsets": "8072,8185", "endColumns": "112,123", "endOffsets": "8180,8304"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5f457b661e995957388e13d6df3e6577\\transformed\\appcompat-1.6.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,910,1001,1094,1189,1283,1383,1476,1571,1679,1770,1861,1944,2058,2166,2266,2380,2487,2595,2755,2854", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "220,326,431,517,627,748,828,905,996,1089,1184,1278,1378,1471,1566,1674,1765,1856,1939,2053,2161,2261,2375,2482,2590,2750,2849,2933"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,435,541,646,732,842,963,1043,1120,1211,1304,1399,1493,1593,1686,1781,1889,1980,2071,2154,2268,2376,2476,2590,2697,2805,2965,8395", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "430,536,641,727,837,958,1038,1115,1206,1299,1394,1488,1588,1681,1776,1884,1975,2066,2149,2263,2371,2471,2585,2692,2800,2960,3059,8474"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9850f61f88e3ad6016154401f7e52e36\\transformed\\core-1.9.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "98", "startColumns": "4", "startOffsets": "8479", "endColumns": "100", "endOffsets": "8575"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e49af50215389496670110435eccf411\\transformed\\material-1.8.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,423,506,628,738,833,966,1055,1121,1218,1298,1360,1449,1512,1577,1636,1709,1772,1826,1954,2011,2073,2127,2200,2343,2427,2515,2651,2739,2827,2912,2965,3016,3082,3157,3233,3319,3396,3472,3549,3623,3714,3789,3880,3972,4046,4133,4224,4279,4345,4428,4514,4576,4640,4703,4820,4933,5044,5161,5218,5273", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,78,78,82,121,109,94,132,88,65,96,79,61,88,62,64,58,72,62,53,127,56,61,53,72,142,83,87,135,87,87,84,52,50,65,74,75,85,76,75,76,73,90,74,90,91,73,86,90,54,65,82,85,61,63,62,116,112,110,116,56,54,85", "endOffsets": "260,339,418,501,623,733,828,961,1050,1116,1213,1293,1355,1444,1507,1572,1631,1704,1767,1821,1949,2006,2068,2122,2195,2338,2422,2510,2646,2734,2822,2907,2960,3011,3077,3152,3228,3314,3391,3467,3544,3618,3709,3784,3875,3967,4041,4128,4219,4274,4340,4423,4509,4571,4635,4698,4815,4928,5039,5156,5213,5268,5354"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3064,3143,3222,3305,3427,3537,3632,3765,3854,3920,4017,4097,4159,4248,4311,4376,4435,4508,4571,4625,4753,4810,4872,4926,4999,5142,5226,5314,5450,5538,5626,5711,5764,5815,5881,5956,6032,6118,6195,6271,6348,6422,6513,6588,6679,6771,6845,6932,7023,7078,7144,7227,7313,7375,7439,7502,7619,7732,7843,7960,8017,8309", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,96", "endColumns": "12,78,78,82,121,109,94,132,88,65,96,79,61,88,62,64,58,72,62,53,127,56,61,53,72,142,83,87,135,87,87,84,52,50,65,74,75,85,76,75,76,73,90,74,90,91,73,86,90,54,65,82,85,61,63,62,116,112,110,116,56,54,85", "endOffsets": "310,3138,3217,3300,3422,3532,3627,3760,3849,3915,4012,4092,4154,4243,4306,4371,4430,4503,4566,4620,4748,4805,4867,4921,4994,5137,5221,5309,5445,5533,5621,5706,5759,5810,5876,5951,6027,6113,6190,6266,6343,6417,6508,6583,6674,6766,6840,6927,7018,7073,7139,7222,7308,7370,7434,7497,7614,7727,7838,7955,8012,8067,8390"}}]}]}