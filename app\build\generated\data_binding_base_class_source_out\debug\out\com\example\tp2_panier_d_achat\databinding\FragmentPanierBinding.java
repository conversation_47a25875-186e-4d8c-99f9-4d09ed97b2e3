// Generated by view binder compiler. Do not edit!
package com.example.tp2_panier_d_achat.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.tp2_panier_d_achat.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentPanierBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button buttonValiderCommande;

  @NonNull
  public final Button buttonViderPanier;

  @NonNull
  public final ConstraintLayout layoutPanierContent;

  @NonNull
  public final LinearLayout layoutPanierVide;

  @NonNull
  public final LinearLayout layoutTotal;

  @NonNull
  public final RecyclerView recyclerViewPanier;

  @NonNull
  public final TextView textViewNombreItems;

  @NonNull
  public final TextView textViewSousTotal;

  @NonNull
  public final TextView textViewTaxe;

  @NonNull
  public final TextView textViewTitrePanier;

  @NonNull
  public final TextView textViewTotal;

  private FragmentPanierBinding(@NonNull ConstraintLayout rootView,
      @NonNull Button buttonValiderCommande, @NonNull Button buttonViderPanier,
      @NonNull ConstraintLayout layoutPanierContent, @NonNull LinearLayout layoutPanierVide,
      @NonNull LinearLayout layoutTotal, @NonNull RecyclerView recyclerViewPanier,
      @NonNull TextView textViewNombreItems, @NonNull TextView textViewSousTotal,
      @NonNull TextView textViewTaxe, @NonNull TextView textViewTitrePanier,
      @NonNull TextView textViewTotal) {
    this.rootView = rootView;
    this.buttonValiderCommande = buttonValiderCommande;
    this.buttonViderPanier = buttonViderPanier;
    this.layoutPanierContent = layoutPanierContent;
    this.layoutPanierVide = layoutPanierVide;
    this.layoutTotal = layoutTotal;
    this.recyclerViewPanier = recyclerViewPanier;
    this.textViewNombreItems = textViewNombreItems;
    this.textViewSousTotal = textViewSousTotal;
    this.textViewTaxe = textViewTaxe;
    this.textViewTitrePanier = textViewTitrePanier;
    this.textViewTotal = textViewTotal;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentPanierBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentPanierBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_panier, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentPanierBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_valider_commande;
      Button buttonValiderCommande = ViewBindings.findChildViewById(rootView, id);
      if (buttonValiderCommande == null) {
        break missingId;
      }

      id = R.id.button_vider_panier;
      Button buttonViderPanier = ViewBindings.findChildViewById(rootView, id);
      if (buttonViderPanier == null) {
        break missingId;
      }

      id = R.id.layout_panier_content;
      ConstraintLayout layoutPanierContent = ViewBindings.findChildViewById(rootView, id);
      if (layoutPanierContent == null) {
        break missingId;
      }

      id = R.id.layout_panier_vide;
      LinearLayout layoutPanierVide = ViewBindings.findChildViewById(rootView, id);
      if (layoutPanierVide == null) {
        break missingId;
      }

      id = R.id.layout_total;
      LinearLayout layoutTotal = ViewBindings.findChildViewById(rootView, id);
      if (layoutTotal == null) {
        break missingId;
      }

      id = R.id.recycler_view_panier;
      RecyclerView recyclerViewPanier = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewPanier == null) {
        break missingId;
      }

      id = R.id.text_view_nombre_items;
      TextView textViewNombreItems = ViewBindings.findChildViewById(rootView, id);
      if (textViewNombreItems == null) {
        break missingId;
      }

      id = R.id.text_view_sous_total;
      TextView textViewSousTotal = ViewBindings.findChildViewById(rootView, id);
      if (textViewSousTotal == null) {
        break missingId;
      }

      id = R.id.text_view_taxe;
      TextView textViewTaxe = ViewBindings.findChildViewById(rootView, id);
      if (textViewTaxe == null) {
        break missingId;
      }

      id = R.id.text_view_titre_panier;
      TextView textViewTitrePanier = ViewBindings.findChildViewById(rootView, id);
      if (textViewTitrePanier == null) {
        break missingId;
      }

      id = R.id.text_view_total;
      TextView textViewTotal = ViewBindings.findChildViewById(rootView, id);
      if (textViewTotal == null) {
        break missingId;
      }

      return new FragmentPanierBinding((ConstraintLayout) rootView, buttonValiderCommande,
          buttonViderPanier, layoutPanierContent, layoutPanierVide, layoutTotal, recyclerViewPanier,
          textViewNombreItems, textViewSousTotal, textViewTaxe, textViewTitrePanier, textViewTotal);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
