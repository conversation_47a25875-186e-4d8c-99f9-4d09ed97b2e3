package com.example.tp2_panier_d_achat.ui.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.tp2_panier_d_achat.R
import com.example.tp2_panier_d_achat.data.PanierItem
import com.example.tp2_panier_d_achat.databinding.ItemPanierBinding
import com.example.tp2_panier_d_achat.utils.CategoryIconHelper

/**
 * Adapter pour le RecyclerView du fragment Panier
 * Gère l'affichage des items du panier et les interactions utilisateur
 */
class PanierAdapter(
    private val onQuantiteChanged: (Long, Int) -> Unit,
    private val onItemRemoved: (Long) -> Unit
) : ListAdapter<PanierItem, PanierAdapter.PanierViewHolder>(PanierItemDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PanierViewHolder {
        val binding = ItemPanierBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return PanierViewHolder(binding)
    }

    override fun onBindViewHolder(holder: PanierViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class PanierViewHolder(
        private val binding: ItemPanierBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(panierItem: PanierItem) {
            binding.apply {
                val item = panierItem.item

                // Afficher les informations de l'item
                textViewNomPanier.text = item.nom
                textViewDescriptionPanier.text = item.description
                textViewPrixUnitaire.text = String.format("%.2f$ / unité", item.prix)
                textViewQuantite.text = panierItem.quantite.toString()
                textViewPrixTotal.text = String.format("%.2f$", panierItem.getPrixTotal())

                // Définir l'image selon la catégorie
                val imageResource = CategoryIconHelper.getIconResource(item.categorie)
                imageViewItemPanier.setImageResource(imageResource)
                imageViewItemPanier.contentDescription = CategoryIconHelper.getIconDescription(item.categorie)

                // Gestion des boutons de quantité
                buttonDiminuer.setOnClickListener {
                    if (panierItem.quantite > 1) {
                        onQuantiteChanged(item.id, panierItem.quantite - 1)
                    }
                }

                buttonAugmenter.setOnClickListener {
                    if (panierItem.quantite < 20) { // Limite maximale
                        onQuantiteChanged(item.id, panierItem.quantite + 1)
                    }
                }

                // Bouton de suppression
                buttonSupprimer.setOnClickListener {
                    onItemRemoved(item.id)
                }

                // Désactiver le bouton diminuer si quantité = 1
                buttonDiminuer.isEnabled = panierItem.quantite > 1
                buttonDiminuer.alpha = if (panierItem.quantite > 1) 1.0f else 0.5f

                // Désactiver le bouton augmenter si quantité = 20
                buttonAugmenter.isEnabled = panierItem.quantite < 20
                buttonAugmenter.alpha = if (panierItem.quantite < 20) 1.0f else 0.5f
            }
        }


    }

    /**
     * DiffCallback pour optimiser les mises à jour de la liste
     */
    class PanierItemDiffCallback : DiffUtil.ItemCallback<PanierItem>() {
        override fun areItemsTheSame(oldItem: PanierItem, newItem: PanierItem): Boolean {
            return oldItem.item.id == newItem.item.id
        }

        override fun areContentsTheSame(oldItem: PanierItem, newItem: PanierItem): Boolean {
            return oldItem == newItem
        }
    }
}
