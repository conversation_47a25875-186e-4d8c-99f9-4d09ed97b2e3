<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.appbar.MaterialToolbar xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/toolbar"
    android:layout_width="match_parent"
    android:layout_height="?attr/actionBarSize"
    android:background="?attr/colorPrimary"
    android:elevation="4dp"
    android:theme="@style/ThemeOverlay.MaterialComponents.Dark.ActionBar"
    app:popupTheme="@style/ThemeOverlay.MaterialComponents.Light"
    app:titleTextColor="?attr/colorOnPrimary"
    app:subtitleTextColor="?attr/colorOnPrimary">

    <!-- Layout personnalisé pour le contenu de la toolbar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <!-- Logo de l'application -->
        <ImageView
            android:id="@+id/toolbar_logo"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginEnd="12dp"
            android:contentDescription="@string/app_name"
            android:src="@drawable/ic_app_logo"
            app:tint="?attr/colorOnPrimary" />

        <!-- Titre principal -->
        <TextView
            android:id="@+id/toolbar_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/app_name"
            android:textColor="?attr/colorOnPrimary"
            android:textSize="20sp"
            android:textStyle="bold" />

        <!-- Indicateur mode admin (optionnel) -->
        <TextView
            android:id="@+id/toolbar_admin_indicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:background="@drawable/bg_admin_indicator"
            android:paddingHorizontal="8dp"
            android:paddingVertical="4dp"
            android:text="🔧 ADMIN"
            android:textColor="@android:color/white"
            android:textSize="12sp"
            android:textStyle="bold"
            android:visibility="gone" />

    </LinearLayout>

</com.google.android.material.appbar.MaterialToolbar>
