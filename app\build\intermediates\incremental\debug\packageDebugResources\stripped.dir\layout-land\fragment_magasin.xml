<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.magasin.MagasinFragment">

    <!-- Logo et titre du fragment (compact en paysage) -->
    <LinearLayout
        android:id="@+id/layout_header"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="8dp"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/image_view_logo"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginEnd="6dp"
            android:contentDescription="@string/app_name"
            android:src="@drawable/ic_app_logo" />

        <TextView
            android:id="@+id/text_view_titre_magasin"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/titre_principal"
            android:textColor="?android:attr/textColorPrimary"
            android:textSize="16sp"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- RecyclerView pour afficher la liste des items (3 colonnes en paysage) -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view_magasin"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="4dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="4dp"
        android:layout_marginBottom="8dp"
        android:clipToPadding="false"
        android:paddingBottom="8dp"
        android:scrollbars="vertical"
        android:scrollbarStyle="outsideOverlay"
        android:scrollbarThumbVertical="@drawable/scrollbar_thumb"
        android:scrollbarTrackVertical="@drawable/scrollbar_track"
        android:fadeScrollbars="false"
        android:overScrollMode="ifContentScrolls"
        android:nestedScrollingEnabled="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layout_header"
        tools:listitem="@layout/item_magasin" />

    <!-- FAB pour ajouter un nouvel item (visible seulement en mode admin) -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_ajouter_item"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:contentDescription="@string/ajouter_item"
        android:src="@drawable/ic_add"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:visibility="visible" />

    <!-- Layout pour liste vide (visible quand aucun item n'est disponible) -->
    <LinearLayout
        android:id="@+id/layout_liste_vide"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/text_view_titre_magasin">

        <ImageView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginBottom="16dp"
            android:alpha="0.5"
            android:src="@drawable/ic_shopping_cart_empty"
            app:tint="?android:attr/textColorSecondary" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/msg_liste_vide"
            android:textColor="?android:attr/textColorSecondary"
            android:textSize="16sp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
