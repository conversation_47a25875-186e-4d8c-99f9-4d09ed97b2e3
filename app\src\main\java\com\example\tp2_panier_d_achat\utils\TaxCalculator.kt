package com.example.tp2_panier_d_achat.utils

/**
 * Utilitaire pour les calculs de taxes
 */
object TaxCalculator {
    
    // Taux de taxe par défaut (15% - exemple pour le Québec/Canada)
    private const val DEFAULT_TAX_RATE = 0.15
    
    /**
     * Calcule le montant de la taxe
     * @param subtotal Sous-total avant taxes
     * @param taxRate Taux de taxe (par défaut 15%)
     * @return Montant de la taxe
     */
    fun calculateTax(subtotal: Double, taxRate: Double = DEFAULT_TAX_RATE): Double {
        return subtotal * taxRate
    }
    
    /**
     * Calcule le total avec taxes
     * @param subtotal Sous-total avant taxes
     * @param taxRate Taux de taxe (par défaut 15%)
     * @return Total avec taxes
     */
    fun calculateTotalWithTax(subtotal: Double, taxRate: Double = DEFAULT_TAX_RATE): Double {
        return subtotal + calculateTax(subtotal, taxRate)
    }
    
    /**
     * Obtient le taux de taxe formaté pour l'affichage
     * @param taxRate Taux de taxe
     * @return Taux formaté (ex: "15%")
     */
    fun formatTaxRate(taxRate: Double = DEFAULT_TAX_RATE): String {
        return "${(taxRate * 100).toInt()}%"
    }
    
    /**
     * Données de calcul détaillées
     */
    data class TaxCalculation(
        val subtotal: Double,
        val taxAmount: Double,
        val taxRate: Double,
        val total: Double
    ) {
        fun getFormattedSubtotal(): String = String.format("%.2f", subtotal)
        fun getFormattedTax(): String = String.format("%.2f", taxAmount)
        fun getFormattedTotal(): String = String.format("%.2f", total)
        fun getFormattedTaxRate(): String = "${(taxRate * 100).toInt()}%"
    }
    
    /**
     * Calcule tous les détails de taxation
     * @param subtotal Sous-total avant taxes
     * @param taxRate Taux de taxe (par défaut 15%)
     * @return Objet TaxCalculation avec tous les détails
     */
    fun calculateDetailed(subtotal: Double, taxRate: Double = DEFAULT_TAX_RATE): TaxCalculation {
        val taxAmount = calculateTax(subtotal, taxRate)
        val total = subtotal + taxAmount
        
        return TaxCalculation(
            subtotal = subtotal,
            taxAmount = taxAmount,
            taxRate = taxRate,
            total = total
        )
    }
}
