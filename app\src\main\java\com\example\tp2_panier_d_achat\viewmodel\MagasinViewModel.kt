package com.example.tp2_panier_d_achat.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.example.tp2_panier_d_achat.data.AppDatabase
import com.example.tp2_panier_d_achat.data.Item
import com.example.tp2_panier_d_achat.data.ItemRepository
import com.example.tp2_panier_d_achat.utils.AdminManager
import kotlinx.coroutines.launch

/**
 * ViewModel pour le fragment Magasin
 * Gère les données et la logique métier pour la liste des items en magasin
 */
class MagasinViewModel(application: Application) : AndroidViewModel(application) {
    
    private val repository: ItemRepository
    
    /**
     * LiveData contenant tous les items du magasin
     */
    val allItems: LiveData<List<Item>>
    
    /**
     * Gestionnaire admin centralisé
     */
    private val adminManager = AdminManager.getInstance(application)
    val isAdminMode: LiveData<Boolean> = adminManager.isAdminMode
    
    /**
     * LiveData pour les messages d'erreur
     */
    private val _errorMessage = MutableLiveData<String>()
    val errorMessage: LiveData<String> = _errorMessage
    
    /**
     * LiveData pour indiquer si une opération est en cours
     */
    private val _isLoading = MutableLiveData<Boolean>(false)
    val isLoading: LiveData<Boolean> = _isLoading
    
    init {
        val itemDao = AppDatabase.getDatabase(application).itemDao()
        repository = ItemRepository(itemDao)
        allItems = repository.allItems
    }
    
    /**
     * Active ou désactive le mode administrateur
     */
    fun toggleAdminMode() {
        adminManager.toggleAdminMode()
    }

    /**
     * Définit le mode administrateur
     */
    fun setAdminMode(isAdmin: Boolean) {
        adminManager.setAdminMode(isAdmin)
    }
    
    /**
     * Insère un nouvel item dans la base de données
     */
    fun insertItem(item: Item) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                repository.insertItem(item)
            } catch (e: Exception) {
                _errorMessage.value = "Erreur lors de l'ajout de l'item: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * Met à jour un item existant
     */
    fun updateItem(item: Item) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                repository.updateItem(item)
            } catch (e: Exception) {
                _errorMessage.value = "Erreur lors de la modification de l'item: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * Supprime un item de la base de données
     */
    fun deleteItem(item: Item) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                repository.deleteItem(item)
            } catch (e: Exception) {
                _errorMessage.value = "Erreur lors de la suppression de l'item: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * Efface le message d'erreur
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }
}
